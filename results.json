[{"file_path": "test_files\\image_1.jpg", "file_name": "image_1.jpg", "classification": {"category": "receipt", "confidence_score": 0.95, "field_match_score": 0.89, "reasoning": "The document contains fields that are specific to a receipt such as supplier name, supplier address, supplier phone number, supplier company registration, date, total amount, total net, and total tax. The document also mentions 'Tax Invoice' and 'Cashier', which are common in receipts. However, the receipt number is missing.", "matched_fields": ["supplier_name", "supplier_address", "supplier_phone_number", "supplier_company_registration", "date", "total_amount", "total_net", "total_tax"], "missing_critical_fields": ["receipt_number"], "alternative_categories": [{"category": "invoice", "confidence": 0.05}, {"category": "bank_check", "confidence": 0.0}], "overall_confidence_level": "HIGH"}}, {"file_path": "test_files\\image_10.jpg", "file_name": "image_10.jpg", "classification": {"category": "invoice", "confidence_score": 0.95, "field_match_score": 0.9, "reasoning": "The document contains most of the critical fields for an invoice, including supplier name, supplier address, supplier company registration, invoice number, date, total amount, total net, and total tax. However, it lacks the customer name and due date.", "matched_fields": ["supplier_name", "supplier_address", "supplier_company_registration", "invoice_number", "date", "total_amount", "total_net", "total_tax"], "missing_critical_fields": ["customer_name", "due_date"], "alternative_categories": [{"category": "receipt", "confidence": 0.05}, {"category": "bank_check", "confidence": 0.0}], "overall_confidence_level": "HIGH"}}, {"file_path": "test_files\\image_11.jpg", "file_name": "image_11.jpg", "classification": {"category": "receipt", "confidence_score": 0.95, "field_match_score": 0.5, "reasoning": "The document contains information typically found on a receipt such as supplier name, supplier address, date, and total amount. However, it lacks some critical fields such as supplier phone number, supplier company registration, receipt number, total net, and total tax.", "matched_fields": ["supplier_name", "supplier_address", "date", "total_amount"], "missing_critical_fields": ["supplier_phone_number", "supplier_company_registration", "receipt_number", "total_net", "total_tax"], "alternative_categories": [{"category": "invoice", "confidence": 0.05}, {"category": "bank_check", "confidence": 0.0}], "overall_confidence_level": "MEDIUM"}}, {"file_path": "test_files\\image_12.jpg", "file_name": "image_12.jpg", "classification": {"category": "receipt", "confidence_score": 0.95, "field_match_score": 0.67, "reasoning": "The document contains a list of purchased items with their prices, a total amount, and a supplier name and address. It does not request payment, which suggests it is a receipt rather than an invoice. It also does not contain any information related to a bank check.", "matched_fields": ["supplier_name", "supplier_address", "supplier_phone_number", "total_amount", "date"], "missing_critical_fields": ["supplier_company_registration", "receipt_number", "total_net", "total_tax"], "alternative_categories": [{"category": "invoice", "confidence": 0.05}, {"category": "bank_check", "confidence": 0.0}], "overall_confidence_level": "HIGH"}}, {"file_path": "test_files\\image_13.jpg", "file_name": "image_13.jpg", "classification": {"category": "receipt", "confidence_score": 0.95, "field_match_score": 0.89, "reasoning": "The document contains most of the critical fields for a receipt, including supplier name, supplier address, supplier phone number, receipt number, date, total amount, total net, and total tax. It does not contain any fields that are exclusive to an invoice or bank check.", "matched_fields": ["supplier_name", "supplier_address", "supplier_phone_number", "receipt_number", "date", "total_amount", "total_net", "total_tax"], "missing_critical_fields": ["supplier_company_registration"], "alternative_categories": [{"category": "invoice", "confidence": 0.05}, {"category": "bank_check", "confidence": 0.0}], "overall_confidence_level": "HIGH"}}, {"file_path": "test_files\\image_14.jpg", "file_name": "image_14.jpg", "classification": {"category": "receipt", "confidence_score": 0.95, "field_match_score": 0.78, "reasoning": "The document contains fields that are typically found in a receipt, such as supplier name, supplier address, date, total amount, total net, and total tax. The context of the document also suggests a transaction for goods (food and drink) that has already taken place, which is consistent with a receipt.", "matched_fields": ["supplier_name", "supplier_address", "date", "total_amount", "total_net", "total_tax"], "missing_critical_fields": ["supplier_phone_number", "supplier_company_registration", "receipt_number"], "alternative_categories": [{"category": "invoice", "confidence": 0.05}, {"category": "bank_check", "confidence": 0.0}], "overall_confidence_level": "HIGH"}}, {"file_path": "test_files\\image_15.jpg", "file_name": "image_15.jpg", "classification": {"category": "receipt", "confidence_score": 0.95, "field_match_score": 0.89, "reasoning": "The document contains most of the critical fields for a receipt, including supplier name, supplier address, supplier phone number, supplier company registration, receipt number, date, total amount, total net, and total tax. The document does not request payment, which is typical for an invoice, and it does not contain any information about a bank or check, which would be necessary for a bank check.", "matched_fields": ["supplier_name", "supplier_address", "supplier_phone_number", "supplier_company_registration", "receipt_number", "date", "total_amount", "total_net", "total_tax"], "missing_critical_fields": [], "alternative_categories": [{"category": "invoice", "confidence": 0.05}, {"category": "bank_check", "confidence": 0.0}], "overall_confidence_level": "HIGH"}}, {"file_path": "test_files\\image_16.jpg", "file_name": "image_16.jpg", "classification": {"category": "invoice", "confidence_score": 0.95, "field_match_score": 0.9, "reasoning": "The document contains critical fields that match the invoice schema such as supplier name, supplier phone number, supplier company registration, supplier address, invoice number, date, total amount, customer name, and due date. The document also requests payment, which is characteristic of an invoice.", "matched_fields": ["supplier_name", "supplier_phone_number", "supplier_company_registration", "supplier_address", "invoice_number", "date", "total_amount", "customer_name", "due_date"], "missing_critical_fields": ["total_net", "total_tax"], "alternative_categories": [{"category": "receipt", "confidence": 0.05}, {"category": "bank_check", "confidence": 0.0}], "overall_confidence_level": "HIGH"}}, {"file_path": "test_files\\image_17.jpg", "file_name": "image_17.jpg", "classification": {"category": "invoice", "confidence_score": 0.95, "field_match_score": 0.9, "reasoning": "The document contains critical fields that match the invoice schema such as supplier name, supplier phone number, supplier company registration, supplier address, invoice number, date, total amount, customer name, and due date. The document also mentions 'invoice' in the text.", "matched_fields": ["supplier_name", "supplier_phone_number", "supplier_company_registration", "supplier_address", "invoice_number", "date", "total_amount", "customer_name", "due_date"], "missing_critical_fields": ["total_net", "total_tax"], "alternative_categories": [{"category": "receipt", "confidence": 0.05}, {"category": "bank_check", "confidence": 0.0}], "overall_confidence_level": "HIGH"}}, {"file_path": "test_files\\image_18.png", "file_name": "image_18.png", "classification": {"category": "receipt", "confidence_score": 0.95, "field_match_score": 0.55, "reasoning": "The document is a passenger receipt for a flight. It contains details about the flight, the passenger, and the travel dates. It does not request payment, which rules out it being an invoice. It also does not contain any banking information, which rules out it being a bank check.", "matched_fields": ["supplier_name", "supplier_address", "receipt_number", "date"], "missing_critical_fields": ["supplier_phone_number", "supplier_company_registration", "total_amount", "total_net", "total_tax"], "alternative_categories": [{"category": "invoice", "confidence": 0.05}, {"category": "bank_check", "confidence": 0.0}], "overall_confidence_level": "MEDIUM"}}, {"file_path": "test_files\\image_19.jpg", "file_name": "image_19.jpg", "classification": {"category": "receipt", "confidence_score": 0.85, "field_match_score": 0.6, "reasoning": "The document is a passenger receipt for a flight journey. It contains details about the flight, the passenger, and the travel dates. It does not contain any request for payment or bank check details.", "matched_fields": ["supplier_name", "date", "receipt_number"], "missing_critical_fields": ["supplier_address", "supplier_phone_number", "supplier_company_registration", "total_amount", "total_net", "total_tax"], "alternative_categories": [{"category": "invoice", "confidence": 0.1}, {"category": "bank_check", "confidence": 0.05}], "overall_confidence_level": "MEDIUM"}}, {"file_path": "test_files\\image_2.jpg", "file_name": "image_2.jpg", "classification": {"category": "receipt", "confidence_score": 0.95, "field_match_score": 0.89, "reasoning": "The document contains most of the critical fields for a receipt, including supplier name, supplier address, supplier phone number, receipt number, date, total amount, total net, and total tax. The document also includes a statement to verify the receipt before leaving the counter, which is common in receipts.", "matched_fields": ["supplier_name", "supplier_address", "supplier_phone_number", "receipt_number", "date", "total_amount", "total_net", "total_tax"], "missing_critical_fields": ["supplier_company_registration"], "alternative_categories": [{"category": "invoice", "confidence": 0.05}], "overall_confidence_level": "HIGH"}}, {"file_path": "test_files\\image_20.png", "file_name": "image_20.png", "classification": {"category": "receipt", "confidence_score": 0.85, "field_match_score": 0.5, "reasoning": "The document contains a list of purchased items with their prices, a total amount to be paid, and an indication of payment made. This is typical of a receipt. However, it lacks some critical fields such as supplier name, receipt number, and date.", "matched_fields": ["total_amount", "total_net", "total_tax"], "missing_critical_fields": ["supplier_name", "supplier_address", "supplier_phone_number", "supplier_company_registration", "receipt_number", "date"], "alternative_categories": [{"category": "invoice", "confidence": 0.1}, {"category": "bank_check", "confidence": 0.05}], "overall_confidence_level": "MEDIUM"}}, {"file_path": "test_files\\image_21.jpg", "file_name": "image_21.jpg", "classification": {"category": "receipt", "confidence_score": 0.95, "field_match_score": 0.78, "reasoning": "The document contains details typically found in a receipt such as supplier name, address, phone number, date, total amount, and total tax. It lacks a few fields like receipt number, total net, and supplier company registration but the overall context and format align with a receipt.", "matched_fields": ["supplier_name", "supplier_address", "supplier_phone_number", "date", "total_amount", "total_tax"], "missing_critical_fields": ["receipt_number", "total_net", "supplier_company_registration"], "alternative_categories": [{"category": "invoice", "confidence": 0.05}, {"category": "bank_check", "confidence": 0.0}], "overall_confidence_level": "HIGH"}}, {"file_path": "test_files\\image_22.jpg", "file_name": "image_22.jpg", "classification": {"category": "not_expense", "confidence_score": 0.95, "field_match_score": 0.0, "reasoning": "The document appears to be a flight itinerary or booking information, which does not match any of the expense document categories. It lacks the critical fields required for a receipt, invoice, or bank check.", "matched_fields": [], "missing_critical_fields": ["supplier_name", "supplier_address", "supplier_phone_number", "supplier_company_registration", "receipt_number", "date", "total_amount", "total_net", "total_tax", "invoice_number", "customer_name", "due_date", "payer_name", "payer_address", "check_date", "number_amount", "word_amount", "bank_name", "routing_number", "account_number", "check_number"], "alternative_categories": [{"category": "receipt", "confidence": 0.03}, {"category": "invoice", "confidence": 0.02}], "overall_confidence_level": "LOW"}}, {"file_path": "test_files\\Image_23.png", "file_name": "Image_23.png", "classification": {"category": "invoice", "confidence_score": 0.75, "field_match_score": 0.44, "reasoning": "The document contains information about a flight booking, including the total amount due, which is characteristic of an invoice. However, it lacks many critical fields of an invoice, such as supplier details and invoice number.", "matched_fields": ["date", "total_amount"], "missing_critical_fields": ["supplier_name", "supplier_phone_number", "supplier_company_registration", "supplier_address", "invoice_number", "total_net", "total_tax", "customer_name", "due_date"], "alternative_categories": [{"category": "receipt", "confidence": 0.2}, {"category": "not_expense", "confidence": 0.05}], "overall_confidence_level": "MEDIUM"}}, {"file_path": "test_files\\image_24.jpg", "file_name": "image_24.jpg", "classification": {"category": "invoice", "confidence_score": 0.95, "field_match_score": 0.9, "reasoning": "The document contains critical fields such as supplier name, supplier address, invoice number, date, total amount, total net, total tax, customer name, and due date, which are characteristic of an invoice. The context of the document also suggests a request for payment for goods or services.", "matched_fields": ["supplier_name", "supplier_address", "invoice_number", "date", "total_amount", "total_net", "total_tax", "customer_name", "due_date"], "missing_critical_fields": ["supplier_phone_number", "supplier_company_registration"], "alternative_categories": [{"category": "receipt", "confidence": 0.05}, {"category": "bank_check", "confidence": 0.0}], "overall_confidence_level": "HIGH"}}, {"file_path": "test_files\\image_3.jpg", "file_name": "image_3.jpg", "classification": {"category": "receipt", "confidence_score": 0.95, "field_match_score": 0.89, "reasoning": "The document contains critical fields such as supplier name, supplier address, supplier phone number, date, total amount, and total tax which are characteristic of a receipt. Additionally, the context of the document indicates a completed transaction, which is typical of a receipt.", "matched_fields": ["supplier_name", "supplier_address", "supplier_phone_number", "date", "total_amount", "total_tax"], "missing_critical_fields": ["supplier_company_registration", "receipt_number", "total_net"], "alternative_categories": [{"category": "invoice", "confidence": 0.05}, {"category": "bank_check", "confidence": 0.0}], "overall_confidence_level": "HIGH"}}, {"file_path": "test_files\\image_4.jpg", "file_name": "image_4.jpg", "classification": {"category": "receipt", "confidence_score": 0.95, "field_match_score": 0.89, "reasoning": "The document contains critical fields such as supplier name, supplier address, supplier phone number, date, total amount, total net, and total tax which are typical for a receipt. The document also mentions 'TAX INVOICE' but lacks critical invoice fields such as customer name and due date.", "matched_fields": ["supplier_name", "supplier_address", "supplier_phone_number", "date", "total_amount", "total_net", "total_tax"], "missing_critical_fields": ["receipt_number", "supplier_company_registration"], "alternative_categories": [{"category": "invoice", "confidence": 0.05}], "overall_confidence_level": "HIGH"}}, {"file_path": "test_files\\image_5.jpg", "file_name": "image_5.jpg", "classification": {"category": "receipt", "confidence_score": 0.95, "field_match_score": 0.78, "reasoning": "The document contains details typically found on a receipt such as supplier name, phone number, VAT number, list of purchased items with their prices, total amount, and a mention of 'TAX INVOICE'. There is no request for payment or due date, which would be expected in an invoice. Also, there are no details related to a bank check.", "matched_fields": ["supplier_name", "supplier_phone_number", "supplier_company_registration", "total_amount", "total_net", "total_tax"], "missing_critical_fields": ["supplier_address", "receipt_number", "date"], "alternative_categories": [{"category": "invoice", "confidence": 0.05}, {"category": "bank_check", "confidence": 0.0}], "overall_confidence_level": "HIGH"}}, {"file_path": "test_files\\image_6.jpg", "file_name": "image_6.jpg", "classification": {"category": "receipt", "confidence_score": 0.95, "field_match_score": 0.89, "reasoning": "The document contains critical fields that match the receipt schema such as supplier_name, supplier_address, supplier_phone_number, supplier_company_registration, receipt_number, date, total_amount, total_net, and total_tax. The document also shows a transaction of goods purchased which is typical of a receipt.", "matched_fields": ["supplier_name", "supplier_address", "supplier_phone_number", "supplier_company_registration", "receipt_number", "date", "total_amount", "total_net", "total_tax"], "missing_critical_fields": [], "alternative_categories": [{"category": "invoice", "confidence": 0.05}, {"category": "bank_check", "confidence": 0.0}], "overall_confidence_level": "HIGH"}}, {"file_path": "test_files\\image_7.jpg", "file_name": "image_7.jpg", "classification": {"category": "not_expense", "confidence_score": 0.0, "field_match_score": 0.0, "reasoning": "OCR extraction failed: LlamaParse job timeout after 5 minutes for test_files\\image_7.jpg", "matched_fields": [], "missing_critical_fields": [], "alternative_categories": [], "overall_confidence_level": "LOW"}}, {"file_path": "test_files\\image_8.jpg", "file_name": "image_8.jpg", "classification": {"category": "receipt", "confidence_score": 0.9, "field_match_score": 0.78, "reasoning": "The document contains fields that are typical for a receipt, such as supplier name, supplier phone number, supplier company registration, date, total amount, total net, and total tax. However, it lacks some critical fields like supplier address and receipt number.", "matched_fields": ["supplier_name", "supplier_phone_number", "supplier_company_registration", "date", "total_amount", "total_net", "total_tax"], "missing_critical_fields": ["supplier_address", "receipt_number"], "alternative_categories": [{"category": "invoice", "confidence": 0.1}, {"category": "bank_check", "confidence": 0.0}], "overall_confidence_level": "HIGH"}}, {"file_path": "test_files\\image_9.jpg", "file_name": "image_9.jpg", "classification": {"category": "receipt", "confidence_score": 0.95, "field_match_score": 0.89, "reasoning": "The document contains most of the critical fields for a receipt, including supplier name, supplier address, supplier phone number, supplier company registration, receipt number, date, total amount, total net, and total tax. However, it lacks the customer name which is not typically required in a receipt.", "matched_fields": ["supplier_name", "supplier_address", "supplier_phone_number", "supplier_company_registration", "receipt_number", "date", "total_amount", "total_net", "total_tax"], "missing_critical_fields": [], "alternative_categories": [{"category": "invoice", "confidence": 0.05}, {"category": "bank_check", "confidence": 0.0}], "overall_confidence_level": "HIGH"}}]