# Expense File Classifier

Classifies expense documents (receipts, invoices, bank checks) using LlamaParse OCR and OpenAI.

## Quick Start

1. **Set API keys**:
```bash
export LLAMAPARSE_API_KEY="your_llamaparse_key"
export OPENAI_API_KEY="your_openai_key"
```

2. **Install dependencies**:
```bash
pip install -r requirements.txt
```

3. **Add files to test_files/ folder**

4. **Run**:
```bash
python expense_classifier.py
```

## Supported File Types
PDF, DOC, DOCX, PNG, JPG, JPEG, TIFF, BMP, GIF

## Document Categories
- **Receipt**: Proof of purchase documents
- **Invoice**: Payment request documents
- **Bank Check**: Check documents
- **Not Expense**: Non-expense documents

## Output Format

Results include category, confidence scores, reasoning, and matched fields:

```json
{
  "category": "receipt",
  "confidence_score": 0.85,
  "field_match_score": 0.90,
  "reasoning": "Document contains supplier name, date, total amount...",
  "matched_fields": ["supplier_name", "date", "total_amount"],
  "missing_critical_fields": ["supplier_address"],
  "overall_confidence_level": "HIGH"
}
```

## Files

- `expense_classifier.py` - Main classifier
- `llamaparse_extractor.py` - Your OCR implementation
- `receipts.json`, `invoice.json`, `bank_check.json` - Document schemas
- `test_files/` - Place documents here
- `results.json` - Classification output
