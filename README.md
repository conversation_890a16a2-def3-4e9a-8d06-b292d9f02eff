# Expense File Classifier

A robust document classifier that uses OCR (LlamaParse) to extract markdown from files, then uses JSON schemas and LLM reasoning to classify expense documents with a multi-score confidence system.

## Features

- **Real LlamaParse Integration**: Uses your existing llamaparse_extractor for OCR
- **Schema-driven Classification**: Uses your 3 existing JSON schemas (receipts, invoice, bank_check)
- **Multi-Score Confidence System**: Provides detailed confidence metrics for classification decisions
- **Production Ready**: No mock implementations - requires real API keys and LLM clients
- **Batch Processing**: Process entire folders of documents at once
- **Detailed Reporting**: Get comprehensive classification results with reasoning

## Supported Document Types

- **Receipt**: Proof of purchase documents with supplier info, items, and payment details
- **Invoice**: Payment request documents with billing information and due dates  
- **Bank Check**: Check documents with payer, payee, and amount information
- **Not Expense**: Documents that don't fit expense categories

## Prerequisites

1. **API Keys Required**:
   - `LLAMAPARSE_API_KEY` - For OCR functionality
   - `OPENAI_API_KEY` or `ANTHROPIC_API_KEY` - For LLM classification

2. **Install dependencies**:
```bash
pip install -r requirements.txt
```

3. **Set environment variables**:
```bash
export LLAMAPARSE_API_KEY="your_llamaparse_key"
export OPENAI_API_KEY="your_openai_key"
# OR
export ANTHROPIC_API_KEY="your_anthropic_key"
```

## Usage

### Production Usage

```python
import asyncio
import os
from expense_classifier import ExpenseFileClassifier
from integration_guide import OpenAIClassifier

async def classify_documents():
    # Set up LLM client
    llm_client = OpenAIClassifier(os.getenv("OPENAI_API_KEY"))

    # Initialize classifier with real integrations
    classifier = ExpenseFileClassifier(
        schema_dir=".",
        llm_client=llm_client,
        llamaparse_api_key=os.getenv("LLAMAPARSE_API_KEY")
    )

    # Classify a single file
    result = await classifier.classify_file("document.pdf")

    print(f"Category: {result.category}")
    print(f"Confidence: {result.confidence_score:.2f}")
    print(f"Reasoning: {result.reasoning}")

# Run the classification
asyncio.run(classify_documents())
```

### Batch Processing

```python
async def classify_folder():
    classifier = ExpenseFileClassifier(schema_dir=".")
    
    # Process all files in a folder
    results = await classifier.classify_batch(
        folder_path="test_files",
        output_file="results.json"
    )
    
    # Print summary
    classifier.print_summary(results)

asyncio.run(classify_folder())
```

### Quick Start

1. **Set up environment**:
```bash
export LLAMAPARSE_API_KEY="your_key"
export OPENAI_API_KEY="your_key"
```

2. **Test the setup**:
```bash
python production_example.py quick
```

3. **Run full demo**:
```bash
python production_example.py
```

## Configuration

### Schema Files

The classifier uses three JSON schema files:
- `receipts.json` - Defines receipt document structure
- `invoice.json` - Defines invoice document structure  
- `bank_check.json` - Defines bank check document structure

### Confidence Levels

- **HIGH (≥0.8)**: Auto-process with confidence
- **MEDIUM (0.5-0.8)**: Flag for review
- **LOW (<0.5)**: Require human verification

## Output Format

```json
{
  "category": "receipt",
  "confidence_score": 0.85,
  "field_match_score": 0.90,
  "reasoning": "Document contains supplier name, date, total amount, and line items typical of a receipt",
  "matched_fields": ["supplier_name", "date", "total_amount", "line_items"],
  "missing_critical_fields": ["supplier_address"],
  "alternative_categories": [
    {"category": "invoice", "confidence": 0.10},
    {"category": "bank_check", "confidence": 0.05}
  ],
  "overall_confidence_level": "HIGH"
}
```

## Integration Notes

### LlamaParse Integration

Replace the mock OCR implementation with actual LlamaParse:

```python
from llama_parse import LlamaParse

class LlamaParseOCR:
    def __init__(self, api_key: str):
        self.parser = LlamaParse(api_key=api_key)
    
    async def extract_markdown(self, file_path: str) -> str:
        documents = await self.parser.aload_data(file_path)
        return documents[0].text
```

### LLM Client Integration

Replace the mock classification with your preferred LLM:

```python
# For OpenAI
import openai

# For Anthropic Claude
import anthropic

# For local models
# Use llama-cpp-python, transformers, etc.
```

## File Structure

```
.
├── expense_classifier.py      # Main classifier code
├── receipts.json             # Receipt schema
├── invoice.json              # Invoice schema  
├── bank_check.json           # Bank check schema
├── test_files/               # Place test files here
├── README.md                 # This file
└── classification_results.json  # Output results
```

## Testing

1. Add sample documents to `test_files/` folder
2. Run: `python expense_classifier.py`
3. Check `classification_results.json` for detailed results

## Customization

### Adding New Document Types

1. Create a new JSON schema file (e.g., `expense_report.json`)
2. Add it to the `SchemaLoader.load_schemas()` method
3. Update the classification prompt to include the new category

### Adjusting Confidence Thresholds

Modify the `_determine_confidence_level()` method in `ExpenseClassifier` class.

### Custom Field Importance

Adjust the `_process_schema()` method to define which fields are critical vs optional for each document type.
