#!/usr/bin/env python3
"""
Timing Comparison Script

Compares the performance of both classification workflows:
1. Schema-based workflow (expense_classifier.py)
2. Markdown-only workflow (markdown_classifier.py)

Shows detailed timing breakdowns and performance analysis.
"""

import json
import statistics
from pathlib import Path

def load_timing_data():
    """Load timing data from both workflows"""
    
    # Load schema-based results
    try:
        with open('extraction_results.json', 'r', encoding='utf-8') as f:
            schema_results = json.load(f)
        print(f"✅ Loaded {len(schema_results)} schema-based results")
    except FileNotFoundError:
        print("❌ extraction_results.json not found")
        schema_results = []
    
    # Load markdown-only results
    try:
        with open('result_markdown_only.json', 'r', encoding='utf-8') as f:
            markdown_results = json.load(f)
        print(f"✅ Loaded {len(markdown_results)} markdown-only results")
    except FileNotFoundError:
        print("❌ result_markdown_only.json not found")
        markdown_results = []
    
    return schema_results, markdown_results

def analyze_timing(results, workflow_name):
    """Analyze timing data for a workflow"""
    
    if not results:
        return None
    
    # Extract timing data
    processing_times = []
    ocr_times = []
    classification_times = []
    extraction_times = []
    
    for result in results:
        processing_time = result.get("processing_time_seconds", 0)
        ocr_time = result.get("ocr_time_seconds", 0)
        classification_time = result.get("classification_time_seconds", 0)
        extraction_time = result.get("extraction_time_seconds", 0)
        
        if processing_time > 0:  # Only include valid timing data
            processing_times.append(processing_time)
            ocr_times.append(ocr_time)
            classification_times.append(classification_time)
            extraction_times.append(extraction_time)
    
    if not processing_times:
        return None
    
    # Calculate statistics
    stats = {
        "workflow": workflow_name,
        "total_files": len(processing_times),
        "processing": {
            "total": sum(processing_times),
            "mean": statistics.mean(processing_times),
            "median": statistics.median(processing_times),
            "min": min(processing_times),
            "max": max(processing_times),
            "stdev": statistics.stdev(processing_times) if len(processing_times) > 1 else 0
        },
        "ocr": {
            "total": sum(ocr_times),
            "mean": statistics.mean(ocr_times),
            "median": statistics.median(ocr_times),
            "percentage": (sum(ocr_times) / sum(processing_times)) * 100
        },
        "classification": {
            "total": sum(classification_times),
            "mean": statistics.mean(classification_times),
            "median": statistics.median(classification_times),
            "percentage": (sum(classification_times) / sum(processing_times)) * 100
        }
    }
    
    # Add extraction stats for schema-based workflow
    if extraction_times and any(t > 0 for t in extraction_times):
        stats["extraction"] = {
            "total": sum(extraction_times),
            "mean": statistics.mean(extraction_times),
            "median": statistics.median(extraction_times),
            "percentage": (sum(extraction_times) / sum(processing_times)) * 100
        }
    
    return stats

def print_timing_analysis(stats):
    """Print detailed timing analysis"""
    
    if not stats:
        print("No timing data available")
        return
    
    print(f"\n📊 {stats['workflow'].upper()} TIMING ANALYSIS")
    print("=" * 60)
    print(f"Files processed: {stats['total_files']}")
    
    # Processing times
    proc = stats['processing']
    print(f"\n⏱️  TOTAL PROCESSING TIME:")
    print(f"  Total: {proc['total']:.2f}s")
    print(f"  Mean: {proc['mean']:.2f}s")
    print(f"  Median: {proc['median']:.2f}s")
    print(f"  Range: {proc['min']:.2f}s - {proc['max']:.2f}s")
    print(f"  Std Dev: {proc['stdev']:.2f}s")
    
    # Component breakdown
    print(f"\n🔍 COMPONENT BREAKDOWN:")
    
    ocr = stats['ocr']
    print(f"  OCR: {ocr['mean']:.2f}s ({ocr['percentage']:.1f}%)")
    
    cls = stats['classification']
    print(f"  Classification: {cls['mean']:.2f}s ({cls['percentage']:.1f}%)")
    
    if 'extraction' in stats:
        ext = stats['extraction']
        print(f"  Extraction: {ext['mean']:.2f}s ({ext['percentage']:.1f}%)")
    
    # Performance rating
    if proc['mean'] < 10:
        rating = "🚀 FAST"
    elif proc['mean'] < 30:
        rating = "⚡ MODERATE"
    else:
        rating = "🐌 SLOW"
    
    print(f"\n📈 Performance Rating: {rating}")

def compare_workflows(schema_stats, markdown_stats):
    """Compare the two workflows"""
    
    if not schema_stats or not markdown_stats:
        print("\n❌ Cannot compare - missing data from one or both workflows")
        return
    
    print(f"\n🔄 WORKFLOW COMPARISON")
    print("=" * 60)
    
    # Speed comparison
    schema_speed = schema_stats['processing']['mean']
    markdown_speed = markdown_stats['processing']['mean']
    
    if markdown_speed > 0:
        speedup = schema_speed / markdown_speed
        if speedup > 1:
            print(f"📈 Markdown-only is {speedup:.1f}x FASTER than schema-based")
        else:
            print(f"📈 Schema-based is {1/speedup:.1f}x FASTER than markdown-only")
    
    # Component comparison
    print(f"\n⏱️  AVERAGE PROCESSING TIME:")
    print(f"  Schema-based: {schema_speed:.2f}s")
    print(f"  Markdown-only: {markdown_speed:.2f}s")
    print(f"  Difference: {abs(schema_speed - markdown_speed):.2f}s")
    
    # OCR comparison (should be similar)
    schema_ocr = schema_stats['ocr']['mean']
    markdown_ocr = markdown_stats['ocr']['mean']
    print(f"\n🔍 OCR TIME COMPARISON:")
    print(f"  Schema-based: {schema_ocr:.2f}s")
    print(f"  Markdown-only: {markdown_ocr:.2f}s")
    print(f"  Difference: {abs(schema_ocr - markdown_ocr):.2f}s")
    
    # Classification comparison
    schema_cls = schema_stats['classification']['mean']
    markdown_cls = markdown_stats['classification']['mean']
    print(f"\n🤖 CLASSIFICATION TIME COMPARISON:")
    print(f"  Schema-based: {schema_cls:.2f}s")
    print(f"  Markdown-only: {markdown_cls:.2f}s")
    print(f"  Difference: {abs(schema_cls - markdown_cls):.2f}s")
    
    # Extraction overhead
    if 'extraction' in schema_stats:
        extraction_time = schema_stats['extraction']['mean']
        print(f"\n📋 EXTRACTION OVERHEAD:")
        print(f"  Additional time for data extraction: {extraction_time:.2f}s")
        print(f"  Extraction as % of total: {schema_stats['extraction']['percentage']:.1f}%")

def print_recommendations():
    """Print performance recommendations"""
    
    print(f"\n💡 PERFORMANCE RECOMMENDATIONS")
    print("=" * 60)
    
    print("🚀 FOR SPEED OPTIMIZATION:")
    print("  - Use markdown-only workflow for simple classification")
    print("  - Batch process files to amortize setup costs")
    print("  - Consider parallel processing for large volumes")
    print("  - Cache OCR results for repeated processing")
    
    print("\n📊 FOR PRODUCTION USE:")
    print("  - Schema-based workflow provides more value despite slower speed")
    print("  - Structured data extraction justifies the additional time")
    print("  - Monitor OCR service performance (usually the bottleneck)")
    print("  - Set appropriate timeouts for each component")
    
    print("\n⚖️  COST vs BENEFIT:")
    print("  - Markdown-only: Lower cost, faster, simple classification")
    print("  - Schema-based: Higher cost, slower, rich structured data")
    print("  - Choose based on downstream requirements")

def main():
    """Main analysis function"""
    
    print("⏱️  EXPENSE CLASSIFIER TIMING ANALYSIS")
    print("=" * 60)
    
    # Load data
    schema_results, markdown_results = load_timing_data()
    
    # Analyze each workflow
    schema_stats = analyze_timing(schema_results, "Schema-based Workflow")
    markdown_stats = analyze_timing(markdown_results, "Markdown-only Workflow")
    
    # Print individual analyses
    print_timing_analysis(schema_stats)
    print_timing_analysis(markdown_stats)
    
    # Compare workflows
    compare_workflows(schema_stats, markdown_stats)
    
    # Print recommendations
    print_recommendations()
    
    # Save analysis results
    analysis_results = {
        "schema_based": schema_stats,
        "markdown_only": markdown_stats,
        "comparison_summary": {
            "schema_avg_time": schema_stats['processing']['mean'] if schema_stats else 0,
            "markdown_avg_time": markdown_stats['processing']['mean'] if markdown_stats else 0,
            "speed_difference": abs(
                (schema_stats['processing']['mean'] if schema_stats else 0) - 
                (markdown_stats['processing']['mean'] if markdown_stats else 0)
            )
        }
    }
    
    with open('timing_analysis.json', 'w', encoding='utf-8') as f:
        json.dump(analysis_results, f, indent=2, ensure_ascii=False)
    
    print(f"\n💾 Detailed analysis saved to: timing_analysis.json")

if __name__ == "__main__":
    main()
