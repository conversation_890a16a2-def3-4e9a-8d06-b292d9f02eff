#!/usr/bin/env python3
"""
Simplified Markdown-Only Expense Classifier

This script follows a streamlined workflow:
1. Extract markdown from documents using LlamaParse
2. Use LLM to classify markdown directly (no schema matching)
3. Return simple results: filename, category, reason

Categories: receipt, invoice, bank_check
"""

import os
import json
import asyncio
import logging
from pathlib import Path
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class LlamaParseExtractor:
    """Extract markdown using the existing llamaparse_extractor"""
    
    def __init__(self, api_key: str):
        from llamaparse_extractor import LlamaIndexAPI
        self.api = LlamaIndexAPI(api_key)
        logger.info("LlamaParse extractor initialized")
    
    async def extract_markdown(self, file_path: str) -> str:
        """Extract markdown from file"""
        import pathlib
        import time
        
        file_path_obj = pathlib.Path(file_path)
        
        # Check supported extensions
        from llamaparse_extractor import SUPPORTED_EXTENSIONS
        if file_path_obj.suffix.lower() not in SUPPORTED_EXTENSIONS:
            raise ValueError(f"Unsupported file: {file_path_obj.suffix}")
        
        logger.info(f"Extracting: {file_path}")
        
        # Upload and process
        upload_response = self.api.upload_file(file_path_obj)
        if 'error' in upload_response:
            raise Exception(f"Upload failed: {upload_response['error']}")
        
        job_id = upload_response.get('id') or upload_response.get('job_id')
        if not job_id:
            raise Exception(f"No job_id returned")
        
        # Poll for completion
        for _ in range(60):  # 5 minutes max
            status_response = self.api.get_job_status(job_id)
            status = status_response.get('status')
            
            if status and status.lower() in ('completed', 'success'):
                break
            elif status and status.lower() == 'failed':
                raise Exception(f"Job failed: {status_response}")
            
            time.sleep(5)
        else:
            raise Exception(f"Timeout waiting for job completion")
        
        # Get result
        result_response = self.api.get_job_result_markdown(job_id)
        if 'error' in result_response:
            raise Exception(f"Result fetch failed: {result_response['error']}")
        
        markdown = result_response.get('markdown') or result_response.get('result')
        if not markdown:
            raise Exception(f"No markdown content returned")
        
        logger.info(f"Extracted {len(markdown)} characters")
        return markdown

class SimpleClassifier:
    """Simple LLM-based classifier for markdown content"""
    
    def __init__(self, api_key: str, model: str = "gpt-4"):
        try:
            import openai
            self.client = openai.AsyncOpenAI(api_key=api_key)
            self.model = model
            logger.info(f"LLM classifier initialized: {model}")
        except ImportError:
            raise ImportError("openai package required: pip install openai")
    
    def create_classification_prompt(self, markdown_text: str) -> str:
        """Create a strong classification prompt"""
        
        prompt = f"""You are an expert document classifier specializing in financial documents. 

TASK: Analyze the following document text and classify it into exactly ONE of these categories:
- receipt: A document proving payment was made for goods/services (shows completed transaction)
- invoice: A document requesting payment for goods/services (shows amount due)  
- bank_check: A bank check or cheque document for payment

DOCUMENT TEXT:
{markdown_text}

CLASSIFICATION RULES:
1. RECEIPT indicators: "paid", "thank you", completed transaction, items purchased, change given, payment method shown
2. INVOICE indicators: "due date", "amount due", "please pay", "bill to", payment terms, requesting payment
3. BANK_CHECK indicators: "pay to the order of", check number, routing number, signature line, bank name

ANALYSIS APPROACH:
1. Look for key phrases and document structure
2. Determine if payment was already made (receipt) or being requested (invoice)
3. Check for banking/check-specific elements
4. Consider the overall context and purpose

RESPONSE FORMAT (JSON only):
{{
    "category": "receipt|invoice|bank_check",
    "reason": "Brief explanation of why this category was chosen, mentioning specific indicators found"
}}

IMPORTANT:
- Respond with valid JSON only
- Choose the most appropriate single category
- Base decision on document content and purpose
- Provide clear reasoning with specific evidence

Analyze the document now:"""
        
        return prompt
    
    async def classify_markdown(self, markdown_text: str) -> dict:
        """Classify markdown text using LLM"""
        
        if not markdown_text or len(markdown_text.strip()) < 10:
            return {
                "category": "unknown",
                "reason": "Document text too short or empty"
            }
        
        prompt = self.create_classification_prompt(markdown_text)
        
        try:
            response = await self.client.chat.completions.create(
                model=self.model,
                messages=[
                    {
                        "role": "system", 
                        "content": "You are an expert financial document classifier. Always respond with valid JSON."
                    },
                    {
                        "role": "user", 
                        "content": prompt
                    }
                ],
                temperature=0.1,  # Low temperature for consistency
                max_tokens=500
            )
            
            response_text = response.choices[0].message.content
            
            # Parse JSON response
            try:
                import re
                json_match = re.search(r'\{.*\}', response_text, re.DOTALL)
                if json_match:
                    result = json.loads(json_match.group())
                    return result
                else:
                    raise ValueError("No JSON found in response")
            except json.JSONDecodeError as e:
                logger.error(f"JSON parse error: {e}")
                return {
                    "category": "unknown",
                    "reason": f"LLM response parsing failed: {e}"
                }
                
        except Exception as e:
            logger.error(f"LLM classification failed: {e}")
            return {
                "category": "unknown", 
                "reason": f"Classification error: {e}"
            }

class MarkdownClassifierApp:
    """Main application for markdown-only classification"""
    
    def __init__(self):
        # Get API keys
        self.llamaparse_key = os.getenv("LLAMAPARSE_API_KEY")
        self.openai_key = os.getenv("OPENAI_API_KEY")
        
        if not self.llamaparse_key:
            raise ValueError("LLAMAPARSE_API_KEY required")
        if not self.openai_key:
            raise ValueError("OPENAI_API_KEY required")
        
        # Initialize components
        self.extractor = LlamaParseExtractor(self.llamaparse_key)
        self.classifier = SimpleClassifier(self.openai_key)
        
        logger.info("Markdown classifier app initialized")
    
    async def classify_file(self, file_path: str) -> dict:
        """Classify a single file"""
        
        file_name = Path(file_path).name
        
        try:
            # Extract markdown
            markdown = await self.extractor.extract_markdown(file_path)
            
            # Classify
            classification = await self.classifier.classify_markdown(markdown)
            
            return {
                "filename": file_name,
                "category": classification.get("category", "unknown"),
                "reason": classification.get("reason", "No reason provided")
            }
            
        except Exception as e:
            logger.error(f"Error processing {file_path}: {e}")
            return {
                "filename": file_name,
                "category": "error",
                "reason": f"Processing failed: {str(e)}"
            }
    
    async def classify_folder(self, folder_path: str) -> list:
        """Classify all files in a folder"""
        
        folder = Path(folder_path)
        if not folder.exists():
            raise FileNotFoundError(f"Folder not found: {folder_path}")
        
        # Get supported files
        from llamaparse_extractor import SUPPORTED_EXTENSIONS
        files = [f for f in folder.iterdir() 
                if f.is_file() and f.suffix.lower() in SUPPORTED_EXTENSIONS]
        
        if not files:
            logger.warning(f"No supported files found in {folder_path}")
            return []
        
        logger.info(f"Processing {len(files)} files...")
        
        results = []
        for file_path in files:
            result = await self.classify_file(str(file_path))
            results.append(result)
            
            # Log progress
            category = result["category"]
            logger.info(f"✓ {result['filename']}: {category}")
        
        return results
    
    def save_results(self, results: list, output_file: str = "result_markdown_only.json"):
        """Save results to JSON file"""
        
        try:
            with open(output_file, 'w', encoding='utf-8') as f:
                json.dump(results, f, indent=2, ensure_ascii=False)
            
            logger.info(f"Results saved to {output_file}")
            
        except Exception as e:
            logger.error(f"Error saving results: {e}")
    
    def print_summary(self, results: list):
        """Print classification summary"""
        
        if not results:
            print("No results to summarize")
            return
        
        # Count categories
        category_counts = {}
        for result in results:
            category = result["category"]
            category_counts[category] = category_counts.get(category, 0) + 1
        
        print("\n" + "="*50)
        print("MARKDOWN CLASSIFICATION SUMMARY")
        print("="*50)
        print(f"Total files: {len(results)}")
        print("\nBy Category:")
        for category, count in sorted(category_counts.items()):
            print(f"  {category}: {count}")
        
        print("\nDetailed Results:")
        for result in results:
            print(f"  {result['filename']}: {result['category']}")
            print(f"    → {result['reason'][:80]}...")

async def main():
    """Main function"""
    
    print("🚀 MARKDOWN-ONLY EXPENSE CLASSIFIER")
    print("=" * 50)
    
    try:
        # Initialize app
        app = MarkdownClassifierApp()
        
        # Process test files
        test_folder = "test_files_benchmark"
        if not Path(test_folder).exists():
            print(f"❌ Test folder not found: {test_folder}")
            return
        
        # Classify all files
        results = await app.classify_folder(test_folder)
        
        if results:
            # Save results
            app.save_results(results)
            
            # Print summary
            app.print_summary(results)
            
            print(f"\n✅ Classification complete!")
            print(f"📄 Results saved to: result_markdown_only.json")
        else:
            print("❌ No files processed")
            
    except Exception as e:
        print(f"❌ Error: {e}")
        logger.error(f"Application error: {e}", exc_info=True)

if __name__ == "__main__":
    asyncio.run(main())
