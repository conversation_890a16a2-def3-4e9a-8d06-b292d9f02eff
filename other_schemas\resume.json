[{"name": "name", "type": "string", "title": "Name", "is_array": false, "description": "The full name of the resume owner"}, {"name": "address", "type": "string", "title": "Address", "is_array": false, "description": "The address of the resume owner"}, {"name": "phone_number", "type": "string", "title": "Phone Number", "is_array": false, "description": "The phone number of the resume owner"}, {"name": "email", "type": "string", "title": "Email", "is_array": false, "description": "The email address of the resume owner"}, {"name": "linkedin_profile", "type": "string", "title": "LinkedIn Profile", "is_array": false, "description": "The LinkedIn profile URL of the resume owner"}, {"name": "education", "type": "nested_object", "title": "Education", "is_array": true, "description": "List of educational experiences", "nested_fields": [{"name": "school_name", "type": "string", "title": "School Name", "is_array": false, "description": "The name of the school"}, {"name": "degree", "type": "string", "title": "Degree", "is_array": false, "description": "The degree obtained"}, {"name": "dates_attended", "type": "string", "title": "Dates Attended", "is_array": false, "description": "The dates the person attended the school"}, {"name": "gpa", "type": "number", "title": "GPA", "is_array": false, "description": "The GPA obtained"}, {"name": "relevant_coursework", "type": "string", "title": "Relevant Coursework", "is_array": false, "description": "Relevant coursework taken"}]}, {"name": "experience", "type": "nested_object", "title": "Experience", "is_array": true, "description": "List of work experiences", "nested_fields": [{"name": "company_name", "type": "string", "title": "Company Name", "is_array": false, "description": "The name of the company"}, {"name": "job_title", "type": "string", "title": "Job Title", "is_array": false, "description": "The job title held"}, {"name": "dates_employed", "type": "string", "title": "Dates Employed", "is_array": false, "description": "The dates the person was employed"}, {"name": "responsibilities", "type": "string", "title": "Responsibilities", "is_array": true, "description": "List of responsibilities"}]}, {"name": "skills", "type": "string", "title": "Skills", "is_array": true, "description": "List of skills"}, {"name": "languages", "type": "nested_object", "title": "Languages", "is_array": true, "description": "List of languages and proficiency", "nested_fields": [{"name": "language", "type": "string", "title": "Language", "is_array": false, "description": "The language"}, {"name": "proficiency", "type": "string", "title": "Proficiency", "is_array": false, "description": "The proficiency level"}]}, {"name": "projects", "type": "nested_object", "title": "Projects", "is_array": true, "description": "List of projects", "nested_fields": [{"name": "project_name", "type": "string", "title": "Project Name", "is_array": false, "description": "The name of the project"}, {"name": "description", "type": "string", "title": "Description", "is_array": false, "description": "The description of the project"}, {"name": "technologies_used", "type": "string", "title": "Technologies Used", "is_array": true, "description": "List of technologies used"}]}, {"name": "awards_certifications", "type": "nested_object", "title": "Awards & Certifications", "is_array": true, "description": "List of awards and certifications", "nested_fields": [{"name": "name", "type": "string", "title": "Name", "is_array": false, "description": "The name of the award or certification"}, {"name": "date_received", "type": "date", "title": "Date Received", "is_array": false, "description": "The date the award or certification was received"}]}, {"name": "summary_objective", "type": "string", "title": "Summary/Objective", "is_array": false, "description": "The summary or objective statement"}]