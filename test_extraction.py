#!/usr/bin/env python3
"""
Test script for the enhanced expense classifier with data extraction

This script demonstrates the new workflow:
1. OCR extraction using LlamaParse
2. Document classification 
3. Schema-based structured data extraction
4. JSON output with both classification and extracted data
"""

import asyncio
import os
import json
from dotenv import load_dotenv
from expense_classifier import ExpenseFileClassifier, OpenAILLMClient

load_dotenv()

async def test_single_file_extraction():
    """Test extraction on a single file"""
    
    print("🧪 TESTING SINGLE FILE EXTRACTION")
    print("=" * 50)
    
    # Check API keys
    llamaparse_key = os.getenv("LLAMAPARSE_API_KEY")
    openai_key = os.getenv("OPENAI_API_KEY")
    
    if not llamaparse_key or not openai_key:
        print("❌ Missing API keys")
        return
    
    # Initialize classifier
    try:
        llm_client = OpenAILLMClient(openai_key)
        classifier = ExpenseFileClassifier(
            schema_dir=".",
            llm_client=llm_client,
            llamaparse_api_key=llamaparse_key
        )
        print("✅ Classifier initialized")
    except Exception as e:
        print(f"❌ Error: {e}")
        return
    
    # Test with a single file
    test_file = "test_files_benchmark/image_1.jpg"  # Adjust path as needed
    
    if not os.path.exists(test_file):
        print(f"❌ Test file not found: {test_file}")
        print("Please ensure you have test files in test_files_benchmark/")
        return
    
    try:
        print(f"\n🔍 Processing: {test_file}")
        
        # Run extraction
        result = await classifier.classify_and_extract_file(test_file)
        
        print(f"\n📊 RESULTS:")
        print(f"Classification: {result.get('classification')}")
        print(f"Confidence: {result.get('confidence_score', 0):.2f}")
        print(f"Reasoning: {result.get('reasoning', 'N/A')}")
        
        extracted_data = result.get('extracted_data')
        if extracted_data:
            print(f"\n📋 EXTRACTED DATA:")
            print(json.dumps(extracted_data, indent=2))
        else:
            print(f"\n⚠️ No data extracted")
            if result.get('extraction_note'):
                print(f"Note: {result.get('extraction_note')}")
        
        # Save result
        with open('single_extraction_test.json', 'w', encoding='utf-8') as f:
            json.dump(result, f, indent=2, ensure_ascii=False)
        
        print(f"\n💾 Result saved to: single_extraction_test.json")
        
    except Exception as e:
        print(f"❌ Error processing file: {e}")

async def compare_workflows():
    """Compare the two workflows: schema-based vs markdown-only"""
    
    print("\n🔄 COMPARING WORKFLOWS")
    print("=" * 50)
    
    # Load results from both approaches
    try:
        # Load schema-based results
        with open('extraction_results.json', 'r', encoding='utf-8') as f:
            schema_results = json.load(f)
        print(f"✅ Loaded {len(schema_results)} schema-based results")
    except FileNotFoundError:
        print("❌ extraction_results.json not found - run expense_classifier.py first")
        return
    
    try:
        # Load markdown-only results  
        with open('result_markdown_only.json', 'r', encoding='utf-8') as f:
            markdown_results = json.load(f)
        print(f"✅ Loaded {len(markdown_results)} markdown-only results")
    except FileNotFoundError:
        print("❌ result_markdown_only.json not found - run markdown_classifier.py first")
        return
    
    # Compare results
    print(f"\n📊 WORKFLOW COMPARISON")
    print("-" * 30)
    
    # Create lookup for markdown results
    markdown_lookup = {result['filename']: result for result in markdown_results}
    
    matches = 0
    total = 0
    
    for schema_result in schema_results:
        filename = schema_result.get('file_name')
        schema_category = schema_result.get('classification')
        
        if filename in markdown_lookup:
            markdown_category = markdown_lookup[filename]['category']
            
            match = "✅" if schema_category == markdown_category else "❌"
            print(f"{match} {filename}: Schema={schema_category}, Markdown={markdown_category}")
            
            if schema_category == markdown_category:
                matches += 1
            total += 1
    
    if total > 0:
        accuracy = matches / total
        print(f"\n📈 Agreement: {matches}/{total} ({accuracy:.1%})")
    
    # Show extraction benefits
    extraction_count = sum(1 for r in schema_results if r.get('extracted_data') is not None)
    print(f"📋 Files with extracted data: {extraction_count}/{len(schema_results)}")

def print_workflow_comparison():
    """Print analysis of both workflows"""
    
    print("\n🤔 WORKFLOW ANALYSIS")
    print("=" * 50)
    
    print("📋 SCHEMA-BASED WORKFLOW (expense_classifier.py):")
    print("✅ Pros:")
    print("  - Structured data extraction")
    print("  - Field-level confidence scoring")
    print("  - Detailed schema validation")
    print("  - Rich metadata (matched/missing fields)")
    print("  - Multi-score confidence system")
    print("❌ Cons:")
    print("  - More complex prompts")
    print("  - Higher token usage")
    print("  - Requires schema maintenance")
    print("  - Slower processing")
    
    print("\n📝 MARKDOWN-ONLY WORKFLOW (markdown_classifier.py):")
    print("✅ Pros:")
    print("  - Simple and fast")
    print("  - Lower token usage")
    print("  - Direct classification")
    print("  - Easy to understand")
    print("❌ Cons:")
    print("  - No structured data extraction")
    print("  - Limited confidence metrics")
    print("  - No field validation")
    print("  - Less detailed analysis")
    
    print("\n🎯 RECOMMENDATION:")
    print("Use SCHEMA-BASED for:")
    print("  - Production systems needing structured data")
    print("  - Compliance/audit requirements")
    print("  - Integration with accounting systems")
    print("  - Detailed analysis and reporting")
    
    print("\nUse MARKDOWN-ONLY for:")
    print("  - Quick classification tasks")
    print("  - Prototyping and testing")
    print("  - Simple sorting/filtering")
    print("  - Cost-sensitive applications")

async def main():
    """Main test function"""
    
    print("🧪 EXPENSE CLASSIFIER EXTRACTION TESTING")
    print("=" * 60)
    
    # Test single file extraction
    await test_single_file_extraction()
    
    # Compare workflows
    await compare_workflows()
    
    # Print analysis
    print_workflow_comparison()
    
    print(f"\n🎉 Testing complete!")

if __name__ == "__main__":
    asyncio.run(main())
