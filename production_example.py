#!/usr/bin/env python3
"""
Production Example: Expense File Classifier with Real Integrations

This example shows how to use the expense classifier with:
- Your existing LlamaParse extractor
- Real LLM clients (OpenAI, Anthropic, etc.)
- Production-ready error handling

Prerequisites:
1. Set LLAMAPARSE_API_KEY environment variable
2. Set either OPENAI_API_KEY or ANTHROPIC_API_KEY
3. Install required packages: pip install openai anthropic
"""

import asyncio
import os
from pathlib import Path
from expense_classifier import ExpenseFileClassifier

# Import LLM clients from integration guide
from integration_guide import OpenAIClassifier, AnthropicClassifier

async def setup_classifier():
    """Set up the classifier with real integrations"""
    
    # Get API keys
    llamaparse_key = os.getenv("LLAMAPARSE_API_KEY")
    openai_key = os.getenv("OPENAI_API_KEY")
    anthropic_key = os.getenv("ANTHROPIC_API_KEY")
    
    # Validate prerequisites
    if not llamaparse_key:
        raise ValueError("LLAMAPARSE_API_KEY environment variable is required")
    
    if not openai_key and not anthropic_key:
        raise ValueError("Either OPENAI_API_KEY or ANTHROPIC_API_KEY is required")
    
    # Choose LLM client
    if openai_key:
        print("🤖 Using OpenAI GPT-4 for classification")
        llm_client = OpenAIClassifier(openai_key, model="gpt-4")
    else:
        print("🤖 Using Anthropic Claude for classification")
        llm_client = AnthropicClassifier(anthropic_key)
    
    # Create classifier
    classifier = ExpenseFileClassifier(
        schema_dir=".",
        llm_client=llm_client,
        llamaparse_api_key=llamaparse_key
    )
    
    print("✅ Classifier initialized successfully")
    print(f"📋 Loaded schemas: {classifier.schema_loader.get_all_categories()}")
    
    return classifier

async def classify_single_file(classifier, file_path):
    """Classify a single file"""
    print(f"\n🔍 Processing: {file_path}")
    print("-" * 50)
    
    try:
        result = await classifier.classify_file(file_path)
        
        print(f"📂 Category: {result.category}")
        print(f"🎯 Confidence: {result.confidence_score:.2f}")
        print(f"📊 Field Match: {result.field_match_score:.2f}")
        print(f"🏆 Overall Level: {result.overall_confidence_level}")
        print(f"💭 Reasoning: {result.reasoning}")
        
        if result.matched_fields:
            print(f"✅ Found Fields: {', '.join(result.matched_fields)}")
        
        if result.missing_critical_fields:
            print(f"❌ Missing Fields: {', '.join(result.missing_critical_fields)}")
        
        if result.alternative_categories:
            print("🔄 Alternatives:")
            for alt in result.alternative_categories:
                print(f"   - {alt['category']}: {alt['confidence']:.2f}")
        
        return result
        
    except Exception as e:
        print(f"❌ Error processing {file_path}: {e}")
        return None

async def classify_folder(classifier, folder_path):
    """Classify all files in a folder"""
    print(f"\n📁 Processing folder: {folder_path}")
    print("=" * 60)
    
    try:
        results = await classifier.classify_batch(
            folder_path=folder_path,
            output_file="production_results.json"
        )
        
        print(f"\n📊 Batch processing completed!")
        print(f"💾 Results saved to: production_results.json")
        
        # Print summary
        classifier.print_summary(results)
        
        return results
        
    except Exception as e:
        print(f"❌ Error processing folder {folder_path}: {e}")
        return []

async def demo_workflow():
    """Demonstrate the complete workflow"""
    print("🚀 PRODUCTION EXPENSE CLASSIFIER DEMO")
    print("=" * 60)
    
    try:
        # Setup classifier
        classifier = await setup_classifier()
        
        # Check for test files
        test_folder = Path("test_files")
        if not test_folder.exists():
            print(f"❌ Test folder not found: {test_folder}")
            print("💡 Create test_files/ folder and add some documents")
            return
        
        # Get files to process
        files = list(test_folder.glob("*"))
        if not files:
            print(f"❌ No files found in {test_folder}")
            print("💡 Add some PDF, image, or document files to test_files/")
            return
        
        print(f"\n📄 Found {len(files)} files to process")
        
        # Process first file individually (for detailed output)
        if files:
            await classify_single_file(classifier, str(files[0]))
        
        # Process all files in batch
        if len(files) > 1:
            await classify_folder(classifier, str(test_folder))
        
        print("\n🎉 Demo completed successfully!")
        
    except Exception as e:
        print(f"❌ Demo failed: {e}")
        print("\n🔧 Troubleshooting:")
        print("1. Check your API keys are set correctly")
        print("2. Ensure you have internet connection")
        print("3. Verify your API keys have sufficient credits")
        print("4. Check that llamaparse_extractor.py is in the same directory")

async def quick_test():
    """Quick test with minimal setup"""
    print("⚡ QUICK TEST MODE")
    print("=" * 30)
    
    # Check prerequisites
    llamaparse_key = os.getenv("LLAMAPARSE_API_KEY")
    openai_key = os.getenv("OPENAI_API_KEY")
    anthropic_key = os.getenv("ANTHROPIC_API_KEY")
    
    missing = []
    if not llamaparse_key:
        missing.append("LLAMAPARSE_API_KEY")
    if not openai_key and not anthropic_key:
        missing.append("OPENAI_API_KEY or ANTHROPIC_API_KEY")
    
    if missing:
        print(f"❌ Missing: {', '.join(missing)}")
        print("\n🔧 Setup Instructions:")
        print("export LLAMAPARSE_API_KEY='your_llamaparse_key'")
        print("export OPENAI_API_KEY='your_openai_key'")
        print("# OR")
        print("export ANTHROPIC_API_KEY='your_anthropic_key'")
        return
    
    print("✅ All API keys found")
    print("✅ Ready for production use")
    
    # Test imports
    try:
        from llamaparse_extractor import LlamaIndexAPI, SUPPORTED_EXTENSIONS
        print(f"✅ LlamaParse extractor: OK")
        print(f"📄 Supported types: {', '.join(list(SUPPORTED_EXTENSIONS)[:3])}...")
    except ImportError as e:
        print(f"❌ LlamaParse extractor: {e}")
        return
    
    try:
        from expense_classifier import SchemaLoader
        loader = SchemaLoader(".")
        categories = loader.get_all_categories()
        print(f"✅ Schemas loaded: {categories}")
    except Exception as e:
        print(f"❌ Schema loading: {e}")
        return
    
    print("\n🚀 System ready! Run demo_workflow() for full test.")

if __name__ == "__main__":
    import sys
    
    if len(sys.argv) > 1 and sys.argv[1] == "quick":
        # Quick test mode
        asyncio.run(quick_test())
    else:
        # Full demo
        asyncio.run(demo_workflow())
