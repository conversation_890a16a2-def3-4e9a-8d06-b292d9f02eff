"""
Configuration file for Expense File Classifier

This file contains all configuration settings and can be easily modified
to customize the classifier behavior.
"""

import os
from typing import Dict, Any

# API Configuration
API_KEYS = {
    "llamaparse": os.getenv("LLAMAPARSE_API_KEY", ""),
    "openai": os.getenv("OPENAI_API_KEY", ""),
    "anthropic": os.getenv("ANTHROPIC_API_KEY", ""),
}

# LLM Configuration
LLM_CONFIG = {
    "provider": "openai",  # Options: "openai", "anthropic", "ollama"
    "models": {
        "openai": "gpt-4",
        "anthropic": "claude-3-sonnet-20240229", 
        "ollama": "llama2"
    },
    "temperature": 0.1,  # Low temperature for consistent results
    "max_tokens": 1000,
    "timeout": 30  # seconds
}

# Confidence Thresholds
CONFIDENCE_THRESHOLDS = {
    "high": 0.8,    # Auto-process with confidence
    "medium": 0.5,  # Flag for review
    "low": 0.0      # Require human verification
}

# Schema Configuration
SCHEMA_CONFIG = {
    "schema_dir": ".",
    "schema_files": {
        "receipt": "receipts.json",
        "invoice": "invoice.json",
        "bank_check": "bank_check.json"
    }
}

# File Processing Configuration
FILE_CONFIG = {
    "test_folder": "test_files",
    "output_folder": "results",
    "supported_extensions": [".pdf", ".png", ".jpg", ".jpeg", ".tiff", ".txt"],
    "max_file_size_mb": 50,
    "batch_size": 10  # Number of files to process concurrently
}

# Logging Configuration
LOGGING_CONFIG = {
    "level": "INFO",  # DEBUG, INFO, WARNING, ERROR
    "format": "%(asctime)s - %(name)s - %(levelname)s - %(message)s",
    "file": "classifier.log"
}

# OCR Configuration
OCR_CONFIG = {
    "result_type": "markdown",
    "verbose": True,
    "parsing_instruction": "Extract all text while preserving structure and formatting",
    "max_timeout": 120  # seconds
}

# Classification Prompt Configuration
PROMPT_CONFIG = {
    "system_message": "You are an expert document classifier specializing in expense documents.",
    "include_examples": True,
    "max_prompt_length": 8000,
    "response_format": "json"
}

# Retry Configuration
RETRY_CONFIG = {
    "max_retries": 3,
    "backoff_factor": 2,  # Exponential backoff: 2^attempt seconds
    "retry_on_errors": ["timeout", "rate_limit", "server_error"]
}

# Performance Configuration
PERFORMANCE_CONFIG = {
    "enable_caching": True,
    "cache_ttl_hours": 24,
    "parallel_processing": True,
    "max_concurrent_files": 5
}

def get_config() -> Dict[str, Any]:
    """Get complete configuration dictionary"""
    return {
        "api_keys": API_KEYS,
        "llm": LLM_CONFIG,
        "confidence": CONFIDENCE_THRESHOLDS,
        "schema": SCHEMA_CONFIG,
        "files": FILE_CONFIG,
        "logging": LOGGING_CONFIG,
        "ocr": OCR_CONFIG,
        "prompt": PROMPT_CONFIG,
        "retry": RETRY_CONFIG,
        "performance": PERFORMANCE_CONFIG
    }

def validate_config() -> bool:
    """Validate configuration and check for required settings"""
    errors = []
    
    # Check for required API keys
    if not API_KEYS["llamaparse"]:
        errors.append("LLAMAPARSE_API_KEY not set")
    
    # Check if at least one LLM API key is available
    llm_keys_available = any([
        API_KEYS["openai"],
        API_KEYS["anthropic"]
    ])
    
    if not llm_keys_available and LLM_CONFIG["provider"] != "ollama":
        errors.append("No LLM API key available (set OPENAI_API_KEY or ANTHROPIC_API_KEY)")
    
    # Check schema files exist
    for category, filename in SCHEMA_CONFIG["schema_files"].items():
        schema_path = os.path.join(SCHEMA_CONFIG["schema_dir"], filename)
        if not os.path.exists(schema_path):
            errors.append(f"Schema file not found: {schema_path}")
    
    # Check test folder exists
    if not os.path.exists(FILE_CONFIG["test_folder"]):
        errors.append(f"Test folder not found: {FILE_CONFIG['test_folder']}")
    
    if errors:
        print("Configuration Errors:")
        for error in errors:
            print(f"  - {error}")
        return False
    
    return True

def setup_environment():
    """Set up required directories and files"""
    # Create directories
    os.makedirs(FILE_CONFIG["test_folder"], exist_ok=True)
    os.makedirs(FILE_CONFIG["output_folder"], exist_ok=True)
    
    print(f"✓ Created directory: {FILE_CONFIG['test_folder']}")
    print(f"✓ Created directory: {FILE_CONFIG['output_folder']}")

def print_config_summary():
    """Print a summary of current configuration"""
    print("Expense File Classifier Configuration")
    print("=" * 40)
    
    print(f"LLM Provider: {LLM_CONFIG['provider']}")
    print(f"LLM Model: {LLM_CONFIG['models'][LLM_CONFIG['provider']]}")
    
    print(f"\nConfidence Thresholds:")
    print(f"  High: ≥{CONFIDENCE_THRESHOLDS['high']}")
    print(f"  Medium: ≥{CONFIDENCE_THRESHOLDS['medium']}")
    print(f"  Low: <{CONFIDENCE_THRESHOLDS['medium']}")
    
    print(f"\nFile Processing:")
    print(f"  Test Folder: {FILE_CONFIG['test_folder']}")
    print(f"  Output Folder: {FILE_CONFIG['output_folder']}")
    print(f"  Max File Size: {FILE_CONFIG['max_file_size_mb']} MB")
    
    print(f"\nAPI Keys Status:")
    print(f"  LlamaParse: {'✓' if API_KEYS['llamaparse'] else '✗'}")
    print(f"  OpenAI: {'✓' if API_KEYS['openai'] else '✗'}")
    print(f"  Anthropic: {'✓' if API_KEYS['anthropic'] else '✗'}")

if __name__ == "__main__":
    print_config_summary()
    print("\nValidating configuration...")
    
    if validate_config():
        print("✓ Configuration is valid")
        setup_environment()
    else:
        print("✗ Configuration has errors")
        print("\nTo fix:")
        print("1. Set required environment variables")
        print("2. Ensure schema files exist")
        print("3. Create test folder if needed")
