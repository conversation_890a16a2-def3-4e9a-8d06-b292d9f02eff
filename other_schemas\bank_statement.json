[{"name": "account_holder_name", "type": "string", "title": "Account Holder Name", "is_array": false, "description": "Name of the account holder"}, {"name": "account_number", "type": "string", "title": "Account Number", "is_array": false, "description": "Account number"}, {"name": "statement_period_start_date", "type": "date", "title": "Statement Period Start Date", "is_array": false, "description": "Start date of the statement period"}, {"name": "statement_period_end_date", "type": "date", "title": "Statement Period End Date", "is_array": false, "description": "End date of the statement period"}, {"name": "statement_date", "type": "date", "title": "Statement Date", "is_array": false, "description": "Date the statement was issued"}, {"name": "beginning_balance", "type": "number", "title": "Beginning Balance", "is_array": false, "description": "Beginning balance of the statement period"}, {"name": "ending_balance", "type": "number", "title": "Ending Balance", "is_array": false, "description": "Ending balance of the statement period"}, {"name": "list_of_transactions", "type": "nested_object", "title": "List of Transactions", "is_array": true, "description": "List of transactions", "nested_fields": [{"name": "date", "type": "date", "title": "Date", "is_array": false, "description": "Date of the transaction"}, {"name": "description", "type": "string", "title": "Description", "is_array": false, "description": "Description of the transaction"}, {"name": "amount", "type": "number", "title": "Amount", "is_array": false, "description": "Amount of the transaction"}]}, {"name": "bank_name", "type": "string", "title": "Bank Name", "is_array": false, "description": "Name of the bank"}, {"name": "account_type", "type": "string", "title": "Account Type", "is_array": false, "description": "Type of account"}, {"name": "currency", "type": "string", "title": "<PERSON><PERSON><PERSON><PERSON>", "is_array": false, "description": "Currency of the account"}, {"name": "total_credits", "type": "number", "title": "Total Credits", "is_array": false, "description": "Total credits for the statement period"}, {"name": "total_debits", "type": "number", "title": "Total Debits", "is_array": false, "description": "Total debits for the statement period"}, {"name": "bank_address", "type": "string", "title": "Bank Address", "is_array": false, "description": "Address of the bank"}, {"name": "branch_code", "type": "string", "title": "Branch Code", "is_array": false, "description": "Branch code of the bank"}]