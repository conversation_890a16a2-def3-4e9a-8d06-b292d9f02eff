[{"name": "given_names", "type": "string", "title": "Given Names", "is_array": false, "description": "The given names of the person."}, {"name": "surnames", "type": "string", "title": "Surnames", "is_array": false, "description": "The surnames of the person."}, {"name": "date_of_birth", "type": "date", "title": "Date of Birth", "is_array": false, "description": "The date of birth of the person."}, {"name": "place_of_birth", "type": "string", "title": "Place of Birth", "is_array": false, "description": "The place of birth of the person."}, {"name": "nationality", "type": "string", "title": "Nationality", "is_array": false, "description": "The nationality of the person."}, {"name": "sex", "type": "classification", "title": "Sex", "is_array": false, "description": "The sex of the person.", "classification_values": ["M", "F"]}, {"name": "document_number", "type": "string", "title": "Document Number", "is_array": false, "description": "The document number of the ID."}, {"name": "date_of_issue", "type": "date", "title": "Date of Issue", "is_array": false, "description": "The date when the ID was issued."}, {"name": "date_of_expiry", "type": "date", "title": "Date of Expiry", "is_array": false, "description": "The date when the ID expires."}, {"name": "authority", "type": "string", "title": "Authority", "is_array": false, "description": "The authority that issued the ID."}, {"name": "address", "type": "nested_object", "title": "Address", "is_array": false, "description": "The address of the person.", "nested_fields": [{"name": "street", "type": "string", "title": "Street", "is_array": false, "description": "The street address."}, {"name": "city", "type": "string", "title": "City", "is_array": false, "description": "The city."}, {"name": "state", "type": "string", "title": "State", "is_array": false, "description": "The state or province."}, {"name": "postal_code", "type": "string", "title": "Postal Code", "is_array": false, "description": "The postal code."}, {"name": "country", "type": "string", "title": "Country", "is_array": false, "description": "The country."}]}, {"name": "document_type", "type": "classification", "title": "Document Type", "is_array": false, "description": "The type of ID document.", "classification_values": ["Passport", "National ID", "Driver's License", "Residence Permit"]}]