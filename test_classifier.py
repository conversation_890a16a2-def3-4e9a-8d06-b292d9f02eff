#!/usr/bin/env python3
"""
Test script for the Expense File Classifier

This script demonstrates how to use the classifier and tests it with sample files.
"""

import asyncio
import json
from expense_classifier import ExpenseFileClassifier

async def test_single_file():
    """Test classification of a single file"""
    print("="*60)
    print("TESTING SINGLE FILE CLASSIFICATION")
    print("="*60)
    
    classifier = ExpenseFileClassifier(schema_dir=".")
    
    test_files = [
        "test_files/sample_receipt.txt",
        "test_files/sample_invoice.txt", 
        "test_files/sample_check.txt",
        "test_files/sample_non_expense.txt"
    ]
    
    for file_path in test_files:
        print(f"\nTesting: {file_path}")
        print("-" * 40)
        
        try:
            result = await classifier.classify_file(file_path)
            
            print(f"Category: {result.category}")
            print(f"Confidence Score: {result.confidence_score:.2f}")
            print(f"Field Match Score: {result.field_match_score:.2f}")
            print(f"Overall Confidence: {result.overall_confidence_level}")
            print(f"Reasoning: {result.reasoning}")
            print(f"Matched Fields: {', '.join(result.matched_fields)}")
            
            if result.missing_critical_fields:
                print(f"Missing Critical Fields: {', '.join(result.missing_critical_fields)}")
            
            if result.alternative_categories:
                print("Alternative Categories:")
                for alt in result.alternative_categories:
                    print(f"  - {alt['category']}: {alt['confidence']:.2f}")
                    
        except Exception as e:
            print(f"Error: {e}")

async def test_batch_processing():
    """Test batch processing of all files in test folder"""
    print("\n" + "="*60)
    print("TESTING BATCH PROCESSING")
    print("="*60)
    
    classifier = ExpenseFileClassifier(schema_dir=".")
    
    # Process all files in test folder
    results = await classifier.classify_batch(
        folder_path="test_files",
        output_file="test_results.json"
    )
    
    # Print summary
    classifier.print_summary(results)
    
    print(f"\nDetailed results saved to: test_results.json")

async def test_confidence_levels():
    """Test and demonstrate confidence level determination"""
    print("\n" + "="*60)
    print("CONFIDENCE LEVEL ANALYSIS")
    print("="*60)
    
    classifier = ExpenseFileClassifier(schema_dir=".")
    
    # Test different confidence scenarios
    test_scenarios = [
        {"confidence": 0.95, "field_match": 0.90, "expected": "HIGH"},
        {"confidence": 0.75, "field_match": 0.65, "expected": "MEDIUM"},
        {"confidence": 0.40, "field_match": 0.30, "expected": "LOW"},
        {"confidence": 0.85, "field_match": 0.50, "expected": "MEDIUM"},
    ]
    
    print("Testing confidence level determination:")
    for i, scenario in enumerate(test_scenarios, 1):
        level = classifier.classifier._determine_confidence_level(
            scenario["confidence"], 
            scenario["field_match"]
        )
        print(f"  Scenario {i}: Confidence={scenario['confidence']:.2f}, "
              f"Field Match={scenario['field_match']:.2f} → {level} "
              f"(Expected: {scenario['expected']})")

def display_schema_info():
    """Display information about loaded schemas"""
    print("\n" + "="*60)
    print("SCHEMA INFORMATION")
    print("="*60)
    
    from expense_classifier import SchemaLoader
    loader = SchemaLoader(".")
    
    print(f"Loaded schemas: {loader.get_all_categories()}")
    
    for category in loader.get_all_categories():
        schema = loader.get_schema(category)
        if schema:
            print(f"\n{category.upper()} Schema:")
            print(f"  Critical fields ({len(schema['critical_fields'])}): {', '.join(schema['critical_fields'][:5])}...")
            print(f"  Optional fields ({len(schema['optional_fields'])}): {', '.join(schema['optional_fields'][:5])}...")
            print(f"  Total fields: {len(schema['fields'])}")

async def main():
    """Run all tests"""
    print("EXPENSE FILE CLASSIFIER - TEST SUITE")
    print("="*60)
    
    # Display schema information
    display_schema_info()
    
    # Test confidence level logic
    await test_confidence_levels()
    
    # Test single file classification
    await test_single_file()
    
    # Test batch processing
    await test_batch_processing()
    
    print("\n" + "="*60)
    print("ALL TESTS COMPLETED")
    print("="*60)
    print("\nNext Steps:")
    print("1. Replace mock OCR with actual LlamaParse integration")
    print("2. Replace mock classification with actual LLM client")
    print("3. Add your own test files to test_files/ folder")
    print("4. Adjust confidence thresholds based on your requirements")

if __name__ == "__main__":
    asyncio.run(main())
