#!/usr/bin/env python3
"""
Test script for the Expense File Classifier

This script demonstrates how to use the classifier with real LlamaParse OCR and LLM integration.
Requires LLAMAPARSE_API_KEY and an LLM client to be configured.
"""

import asyncio
import json
import os
from expense_classifier import ExpenseFileClassifier

def check_prerequisites():
    """Check if all prerequisites are met"""
    print("="*60)
    print("CHECKING PREREQUISITES")
    print("="*60)

    # Check API key
    llamaparse_api_key = os.getenv("LLAMAPARSE_API_KEY")
    if not llamaparse_api_key:
        print("❌ LLAMAPARSE_API_KEY not found in environment variables")
        print("Please set: export LLAMAPARSE_API_KEY='your_api_key'")
        return False
    else:
        print(f"✅ LLAMAPARSE_API_KEY found: {llamaparse_api_key[:10]}...")

    # Check LLM client availability
    openai_key = os.getenv("OPENAI_API_KEY")
    anthropic_key = os.getenv("ANTHROPIC_API_KEY")

    if not openai_key and not anthropic_key:
        print("❌ No LLM API key found")
        print("Please set either:")
        print("  export OPENAI_API_KEY='your_openai_key'")
        print("  export ANTHROPIC_API_KEY='your_anthropic_key'")
        return False

    if openai_key:
        print(f"✅ OPENAI_API_KEY found: {openai_key[:10]}...")
    if anthropic_key:
        print(f"✅ ANTHROPIC_API_KEY found: {anthropic_key[:10]}...")

    # Check schemas
    try:
        from expense_classifier import SchemaLoader
        loader = SchemaLoader(".")
        categories = loader.get_all_categories()
        if categories:
            print(f"✅ Schemas loaded: {categories}")
        else:
            print("❌ No schemas found")
            return False
    except Exception as e:
        print(f"❌ Error loading schemas: {e}")
        return False

    # Check llamaparse_extractor
    try:
        from llamaparse_extractor import LlamaIndexAPI, SUPPORTED_EXTENSIONS
        print(f"✅ llamaparse_extractor imported successfully")
        print(f"✅ Supported extensions: {SUPPORTED_EXTENSIONS}")
    except ImportError as e:
        print(f"❌ Error importing llamaparse_extractor: {e}")
        return False

    return True

async def test_single_file():
    """Test classification of a single file - REQUIRES LLM CLIENT"""
    print("\n" + "="*60)
    print("TESTING SINGLE FILE CLASSIFICATION")
    print("="*60)

    print("❌ This test requires an LLM client to be configured.")
    print("💡 Please use integration_guide.py to set up OpenAI, Anthropic, or Ollama.")
    print("💡 Then modify this test to include your LLM client.")
    print("\nExample:")
    print("```python")
    print("from integration_guide import OpenAIClassifier")
    print("llm_client = OpenAIClassifier(os.getenv('OPENAI_API_KEY'))")
    print("classifier = ExpenseFileClassifier(")
    print("    schema_dir='.',")
    print("    llm_client=llm_client,")
    print("    llamaparse_api_key=os.getenv('LLAMAPARSE_API_KEY')")
    print(")")
    print("```")

async def test_batch_processing():
    """Test batch processing - REQUIRES LLM CLIENT"""
    print("\n" + "="*60)
    print("TESTING BATCH PROCESSING")
    print("="*60)

    print("❌ This test requires an LLM client to be configured.")
    print("💡 See integration_guide.py for examples of setting up LLM clients.")
    print("💡 Once configured, you can process entire folders of documents.")

async def test_confidence_levels():
    """Test confidence level determination logic"""
    print("\n" + "="*60)
    print("CONFIDENCE LEVEL ANALYSIS")
    print("="*60)

    # Test the confidence logic without requiring full classifier
    from expense_classifier import ExpenseClassifier, SchemaLoader

    schema_loader = SchemaLoader(".")
    classifier_obj = ExpenseClassifier(schema_loader, None)

    # Test different confidence scenarios
    test_scenarios = [
        {"confidence": 0.95, "field_match": 0.90, "expected": "HIGH"},
        {"confidence": 0.75, "field_match": 0.65, "expected": "MEDIUM"},
        {"confidence": 0.40, "field_match": 0.30, "expected": "LOW"},
        {"confidence": 0.85, "field_match": 0.50, "expected": "MEDIUM"},
    ]

    print("✅ Testing confidence level determination logic:")
    for i, scenario in enumerate(test_scenarios, 1):
        level = classifier_obj._determine_confidence_level(
            scenario["confidence"],
            scenario["field_match"]
        )
        status = "✅" if level == scenario["expected"] else "❌"
        print(f"  {status} Scenario {i}: Confidence={scenario['confidence']:.2f}, "
              f"Field Match={scenario['field_match']:.2f} → {level} "
              f"(Expected: {scenario['expected']})")

def display_schema_info():
    """Display information about loaded schemas"""
    print("\n" + "="*60)
    print("SCHEMA INFORMATION")
    print("="*60)
    
    from expense_classifier import SchemaLoader
    loader = SchemaLoader(".")
    
    print(f"Loaded schemas: {loader.get_all_categories()}")
    
    for category in loader.get_all_categories():
        schema = loader.get_schema(category)
        if schema:
            print(f"\n{category.upper()} Schema:")
            print(f"  Critical fields ({len(schema['critical_fields'])}): {', '.join(schema['critical_fields'][:5])}...")
            print(f"  Optional fields ({len(schema['optional_fields'])}): {', '.join(schema['optional_fields'][:5])}...")
            print(f"  Total fields: {len(schema['fields'])}")

async def main():
    """Run all tests"""
    print("EXPENSE FILE CLASSIFIER - PRODUCTION TEST SUITE")
    print("="*60)

    # Check prerequisites first
    if not check_prerequisites():
        print("\n❌ Prerequisites not met. Please fix the issues above.")
        return

    print("\n✅ All prerequisites met!")

    # Display schema information
    display_schema_info()

    # Test confidence level logic (doesn't require LLM)
    await test_confidence_levels()

    # Test single file classification (requires LLM)
    await test_single_file()

    # Test batch processing (requires LLM)
    await test_batch_processing()

    print("\n" + "="*60)
    print("TEST SUITE COMPLETED")
    print("="*60)
    print("\n🚀 Ready for Production:")
    print("✅ LlamaParse OCR integration active")
    print("✅ Your existing schemas loaded")
    print("✅ Multi-score confidence system ready")
    print("\n📋 Next Steps:")
    print("1. Configure LLM client (see integration_guide.py)")
    print("2. Add your test files to test_files/ folder")
    print("3. Run real classification tests")
    print("4. Adjust confidence thresholds if needed")

if __name__ == "__main__":
    asyncio.run(main())
