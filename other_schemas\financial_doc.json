[{"name": "supplier_name", "type": "string", "title": "Supplier Name", "is_array": false, "description": "The name of the supplier of the financial document."}, {"name": "supplier_phone_number", "type": "string", "title": "Supplier Phone Number", "is_array": false, "description": "The phone number of the supplier of the financial document."}, {"name": "supplier_company_registration", "type": "nested_object", "title": "Supplier Company Registration", "is_array": true, "description": "A list of company registration details including type and number,  for the supplier of the financial document.", "nested_fields": [{"name": "number", "type": "string", "title": "Number", "is_array": false, "description": "The company registration number."}, {"name": "type", "type": "classification", "title": "Type", "is_array": false, "description": "The type of the company registration number.", "classification_values": ["VAT", "SIRET", "SIREN", "NIF", "CF", "UID", "STNR", "HRA_HRB", "TIN", "RFC", "BTW", "ABN", "UEN", "CVR", "ORGNRO", "INN", "DPH", "NIP", "GSTIN", "CRN", "KVK", "DIC", "TAX_ID", "CIF", "GST_HST_CA", "COC"]}]}, {"name": "supplier_address", "type": "nested_object", "title": "Supplier Address", "is_array": false, "description": "The full address of the supplier and the breakdown in components.", "nested_fields": [{"name": "address", "type": "string", "title": "Address", "is_array": false, "description": "The full address."}, {"name": "street_number", "type": "string", "title": "Street Number", "is_array": false, "description": "The street number of the address."}, {"name": "street_name", "type": "string", "title": "Street Name", "is_array": false, "description": "The street name of the address."}, {"name": "po_box", "type": "string", "title": "PO Box", "is_array": false, "description": "The PO Box of the address if there is one."}, {"name": "address_complement", "type": "string", "title": "Address Complement", "is_array": false, "description": "Address complement: floor, building, suite, ..."}, {"name": "city", "type": "string", "title": "City", "is_array": false, "description": "The city of the address."}, {"name": "postal_code", "type": "string", "title": "Postal Code", "is_array": false, "description": "The postal code of the address."}, {"name": "state", "type": "string", "title": "State", "is_array": false, "description": "The state / region / land of the address."}, {"name": "country", "type": "string", "title": "Country", "is_array": false, "description": "The country of the address."}]}, {"name": "invoice_number", "type": "string", "title": "Invoice Number", "is_array": false, "description": "The number of the invoice if the financial document is an invoice.", "extraction_guidelines": "If the document is not an invoice, this is empty."}, {"name": "receipt_number", "type": "string", "title": "Receipt Number", "is_array": false, "description": "The number of the receipt if the document is a receipt.", "extraction_guidelines": "If the document is not a receipt this is empty."}, {"name": "date", "type": "date", "title": "Date", "is_array": false, "description": "The date the document was issued.", "extraction_guidelines": "YYYY-MM-DD format only", "formatting_guidelines": ""}, {"name": "due_date", "type": "date", "title": "Due Date", "is_array": false, "description": "The date on which the payment is due."}, {"name": "total_amount", "type": "number", "title": "Total Amount", "is_array": false, "description": "The final total amount paid, including all taxes and discounts."}, {"name": "total_net", "type": "number", "title": "Total Net", "is_array": false, "description": "The total amount before taxes."}, {"name": "total_tax", "type": "number", "title": "Total Tax", "is_array": false, "description": "The total amount of all taxes."}, {"name": "taxes", "type": "nested_object", "title": "Taxes", "is_array": true, "description": "A list of individual taxes applied, each including rate, base and amount.", "nested_fields": [{"name": "rate", "type": "number", "title": "Rate", "is_array": false, "description": "The tax rate of this tax as a decimal.", "extraction_guidelines": "e.g. 0.15 for 15%"}, {"name": "base", "type": "number", "title": "Base", "is_array": false, "description": "The base amount on which this tax is computed."}, {"name": "amount", "type": "number", "title": "Amount", "is_array": false, "description": "The computed tax amount for this tax."}]}, {"name": "tips_gratuity", "type": "number", "title": "Tips & Gratuity", "is_array": false, "description": "The total tips and gratuities for the document."}, {"name": "line_items", "type": "nested_object", "title": "Line Items", "is_array": true, "description": "A list of line items in the document.", "nested_fields": [{"name": "description", "type": "string", "title": "Description", "is_array": false, "description": "A description of the item or service."}, {"name": "quantity", "type": "number", "title": "Quantity", "is_array": false, "description": "The quantity of the item or service."}, {"name": "unit_price", "type": "number", "title": "Unit Price", "is_array": false, "description": "The price per unit of the item or service."}, {"name": "total_price", "type": "number", "title": "Total Price", "is_array": false, "description": "The total price for the line item: quantity * unit price."}, {"name": "product_code", "type": "string", "title": "Product Code", "is_array": false, "description": "The product code of the item."}, {"name": "tax_amount", "type": "number", "title": "Tax Amount", "is_array": false, "description": "The tax amount of the item."}, {"name": "tax_rate", "type": "number", "title": "Tax Rate", "is_array": false, "description": "The tax rate of the item.", "extraction_guidelines": "Between 0 and 1, e.g. 0.15 for 15%."}, {"name": "unit_measure", "type": "string", "title": "Unit Measure", "is_array": false, "description": "The unit of measure of the item."}]}, {"name": "document_type", "type": "classification", "title": "Document Type", "is_array": false, "description": "Document type of the financial document.", "classification_values": ["invoice", "payslip", "quote", "purchase_order", "statement", "credit_card_receipt", "credit_note", "other_financial", "expense_receipt"]}, {"name": "purchase_category", "type": "classification", "title": "Purchase Category", "is_array": false, "description": "The category of items purchased.", "classification_values": ["food", "gasoline", "parking", "toll", "accommodation", "transport", "telecom", "software", "shopping", "energy", "miscellaneous"]}, {"name": "purchase_subcategory", "type": "classification", "title": "Purchase Subcategory", "is_array": false, "description": "The purchase subcategory", "classification_values": ["restaurant", "delivery", "train", "public", "taxi", "car_rental", "plane", "micromobility", "office_supplies", "electronics", "cultural", "groceries", "other"], "extraction_guidelines": "Only for food, shopping and transport."}, {"name": "locale", "type": "nested_object", "title": "Locale", "is_array": false, "description": "The locale contains the language, country and currency of the document.", "nested_fields": [{"name": "language", "type": "string", "title": "Language", "is_array": false, "description": "The language of the document, ISO 639-1 language code."}, {"name": "country", "type": "string", "title": "Country", "is_array": false, "description": "The country of the document, ISO 3166-1 alpha-2."}, {"name": "currency", "type": "string", "title": "<PERSON><PERSON><PERSON><PERSON>", "is_array": false, "description": "The currency in which is issued the document, ISO 4217."}]}, {"name": "customer_name", "type": "string", "title": "Customer Name", "is_array": false, "description": "The name of the customer of the document."}, {"name": "customer_address", "type": "nested_object", "title": "Customer Address", "is_array": false, "description": "The full address of the customer and the breakdown of the address in components.", "nested_fields": [{"name": "address", "type": "string", "title": "Address", "is_array": false, "description": "The full customer address."}, {"name": "street_number", "type": "string", "title": "Street number", "is_array": false, "description": "The street number of the address."}, {"name": "street_name", "type": "string", "title": "Street Name", "is_array": false, "description": "The street name of the address."}, {"name": "po_box", "type": "string", "title": "PO Box", "is_array": false, "description": "PO Box number, if there is one in the address."}, {"name": "address_complement", "type": "string", "title": "Address Complement", "is_array": false, "description": "Address complement: floor, building, suite, ..."}, {"name": "city", "type": "string", "title": "City", "is_array": false, "description": "The city of the address."}, {"name": "postal_code", "type": "string", "title": "Postal Code", "is_array": false, "description": "The postal code of the address."}, {"name": "state", "type": "string", "title": "State", "is_array": false, "description": "The state / region / land of the address, if there is any."}, {"name": "country", "type": "string", "title": "Country", "is_array": false, "description": "The country of the address."}]}, {"name": "customer_company_registrations", "type": "nested_object", "title": "Customer Company Registrations", "is_array": true, "description": "A list of company registration details including type and number,  for the customer.", "nested_fields": [{"name": "number", "type": "string", "title": "Number", "is_array": false, "description": "The company registration number."}, {"name": "type", "type": "classification", "title": "Type", "is_array": false, "description": "The type of the registration number.", "classification_values": ["VAT", "SIRET", "SIREN", "NIF", "CF", "UID", "STNR", "HRA_HRB", "TIN", "RFC", "BTW", "ABN", "UEN", "CVR", "ORGNRO", "INN", "DPH", "NIP", "GSTIN", "CRN", "KVK", "DIC", "TAX_ID", "CIF", "GST_HST_CA", "COC"]}]}]