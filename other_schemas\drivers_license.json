[{"name": "first_name", "type": "string", "title": "First Name", "is_array": false, "description": "The first name(s) of the driver. Captures all first names if multiple are present (e.g., <PERSON>)."}, {"name": "last_name", "type": "string", "title": "Last Name", "is_array": false, "description": "The last name of the driver."}, {"name": "date_of_birth", "type": "date", "title": "Date of Birth", "is_array": false, "description": "The date of birth of the driver."}, {"name": "license_number", "type": "string", "title": "License Number", "is_array": false, "description": "The driver's license number."}, {"name": "address", "type": "string", "title": "Address", "is_array": false, "description": "The address of the driver."}, {"name": "issue_date", "type": "date", "title": "Issue Date", "is_array": false, "description": "The date the license was issued."}, {"name": "expiration_date", "type": "date", "title": "Expiration Date", "is_array": false, "description": "The date the license expires."}, {"name": "issuing_state_authority", "type": "string", "title": "Issuing State Authority", "is_array": false, "description": "The state or authority that issued the license."}, {"name": "gender", "type": "classification", "title": "Gender", "is_array": false, "description": "The gender of the driver.", "classification_values": ["Male", "Female", "Other"]}, {"name": "restrictions_endorsements", "type": "string", "title": "Restrictions/Endorsements", "is_array": false, "description": "A list of restrictions and endorsements on the license."}, {"name": "mrz", "type": "string", "title": "MRZ", "is_array": false, "description": "Machine Readable Zone"}]