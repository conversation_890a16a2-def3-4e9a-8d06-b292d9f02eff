[{"name": "first_name", "type": "string", "title": "First Name", "is_array": false, "description": "<PERSON><PERSON>loy<PERSON>'s first name"}, {"name": "last_name", "type": "string", "title": "Last Name", "is_array": false, "description": "Employ<PERSON>'s last name"}, {"name": "employee_address", "type": "string", "title": "Employee Address", "is_array": false, "description": "Employee's address"}, {"name": "pay_period_start_date", "type": "date", "title": "Pay Period Start Date", "is_array": false, "description": "Start date of the pay period"}, {"name": "pay_period_end_date", "type": "date", "title": "Pay Period End Date", "is_array": false, "description": "End date of the pay period"}, {"name": "gross_pay", "type": "number", "title": "Gross Pay", "is_array": false, "description": "Gross pay amount"}, {"name": "net_pay", "type": "number", "title": "Net Pay", "is_array": false, "description": "Net pay amount"}, {"name": "deductions", "type": "nested_object", "title": "Deductions", "is_array": true, "description": "List of deductions", "nested_fields": [{"name": "name", "type": "string", "title": "Name", "is_array": false, "description": "Name of the deduction"}, {"name": "amount", "type": "number", "title": "Amount", "is_array": false, "description": "Amount of the deduction"}]}, {"name": "taxes", "type": "nested_object", "title": "Taxes", "is_array": true, "description": "List of taxes", "nested_fields": [{"name": "name", "type": "string", "title": "Name", "is_array": false, "description": "Name of the tax"}, {"name": "amount", "type": "number", "title": "Amount", "is_array": false, "description": "Amount of the tax"}]}, {"name": "employer_name", "type": "string", "title": "Employer Name", "is_array": false, "description": "Name of the employer"}, {"name": "employer_address", "type": "string", "title": "Employer Address", "is_array": false, "description": "Address of the employer"}, {"name": "social_security_number", "type": "string", "title": "Social Security Number", "is_array": false, "description": "Employee's Social Security Number"}, {"name": "employee_id", "type": "string", "title": "Employee ID", "is_array": false, "description": "Employee's ID"}]