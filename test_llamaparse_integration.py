#!/usr/bin/env python3
"""
Test script to verify LlamaParse integration with the Expense File Classifier

This script tests the integration between your existing llamaparse_extractor
and the expense classifier.
"""

import asyncio
import os
from pathlib import Path

async def test_llamaparse_integration():
    """Test the LlamaParse integration"""
    print("="*60)
    print("TESTING LLAMAPARSE INTEGRATION")
    print("="*60)
    
    # Check if API key is available
    api_key = os.getenv("LLAMAPARSE_API_KEY")
    if not api_key:
        print("❌ LLAMAPARSE_API_KEY not found in environment variables")
        print("Please set your LlamaParse API key:")
        print("export LLAMAPARSE_API_KEY='your_api_key_here'")
        return False
    
    print(f"✅ LlamaParse API key found: {api_key[:10]}...")
    
    # Test importing your llamaparse_extractor
    try:
        from llamaparse_extractor import LlamaIndexAPI, SUPPORTED_EXTENSIONS
        print("✅ Successfully imported llamaparse_extractor")
        print(f"✅ Supported extensions: {SUPPORTED_EXTENSIONS}")
    except ImportError as e:
        print(f"❌ Failed to import llamaparse_extractor: {e}")
        return False
    
    # Test creating the API client
    try:
        api = LlamaIndexAPI(api_key)
        print("✅ Successfully created LlamaIndexAPI client")
    except Exception as e:
        print(f"❌ Failed to create LlamaIndexAPI client: {e}")
        return False
    
    # Test importing the updated expense classifier
    try:
        from expense_classifier import LlamaParseOCR, ExpenseFileClassifier
        print("✅ Successfully imported updated expense classifier")
    except ImportError as e:
        print(f"❌ Failed to import expense classifier: {e}")
        return False
    
    # Test creating the OCR client
    try:
        ocr = LlamaParseOCR(api_key)
        print("✅ Successfully created LlamaParseOCR client")
    except Exception as e:
        print(f"❌ Failed to create LlamaParseOCR client: {e}")
        return False
    
    # Test creating the full classifier
    try:
        classifier = ExpenseFileClassifier(
            schema_dir=".",
            llamaparse_api_key=api_key
        )
        print("✅ Successfully created ExpenseFileClassifier")
        print(f"✅ Loaded schemas: {classifier.schema_loader.get_all_categories()}")
    except Exception as e:
        print(f"❌ Failed to create ExpenseFileClassifier: {e}")
        return False
    
    print("\n" + "="*60)
    print("INTEGRATION TEST RESULTS")
    print("="*60)
    print("✅ All integration tests passed!")
    print("✅ Your llamaparse_extractor is successfully integrated")
    print("✅ The expense classifier is ready to use real LlamaParse OCR")
    
    return True

async def test_with_sample_file():
    """Test with a sample file if available"""
    print("\n" + "="*60)
    print("TESTING WITH SAMPLE FILE")
    print("="*60)
    
    api_key = os.getenv("LLAMAPARSE_API_KEY")
    if not api_key:
        print("⚠️  Skipping file test - no API key available")
        return
    
    # Look for test files
    test_folder = Path("test_files")
    if not test_folder.exists():
        print("⚠️  No test_files folder found")
        return
    
    # Find a text file for testing (since we don't want to use API credits unnecessarily)
    text_files = list(test_folder.glob("*.txt"))
    if not text_files:
        print("⚠️  No .txt files found in test_files folder")
        return
    
    test_file = text_files[0]
    print(f"📄 Testing with file: {test_file}")
    
    try:
        from expense_classifier import ExpenseFileClassifier
        classifier = ExpenseFileClassifier(
            schema_dir=".",
            llamaparse_api_key=api_key
        )
        
        print(f"🔄 Processing {test_file.name}...")
        
        # Note: This will actually call LlamaParse API
        # Comment out the next line if you don't want to use API credits
        # result = await classifier.classify_file(str(test_file))
        # print(f"✅ Classification result: {result.category} (confidence: {result.confidence_score:.2f})")
        
        print("⚠️  Actual API call commented out to preserve credits")
        print("⚠️  Uncomment the API call in test_llamaparse_integration.py to test fully")
        
    except Exception as e:
        print(f"❌ Error during file processing: {e}")

def print_usage_instructions():
    """Print instructions for using the integrated classifier"""
    print("\n" + "="*60)
    print("USAGE INSTRUCTIONS")
    print("="*60)
    print("1. Set your LlamaParse API key:")
    print("   export LLAMAPARSE_API_KEY='your_api_key_here'")
    print("")
    print("2. Use the classifier in your code:")
    print("   ```python")
    print("   from expense_classifier import ExpenseFileClassifier")
    print("   import asyncio")
    print("")
    print("   async def classify_documents():")
    print("       classifier = ExpenseFileClassifier(")
    print("           schema_dir='.',")
    print("           llamaparse_api_key='your_api_key'")
    print("       )")
    print("       result = await classifier.classify_file('document.pdf')")
    print("       print(f'Category: {result.category}')")
    print("")
    print("   asyncio.run(classify_documents())")
    print("   ```")
    print("")
    print("3. For batch processing:")
    print("   ```python")
    print("   results = await classifier.classify_batch('folder_path')")
    print("   classifier.print_summary(results)")
    print("   ```")
    print("")
    print("4. Supported file types:")
    print("   PDF, DOC, DOCX, PNG, JPG, JPEG, TIFF, BMP, GIF")

async def main():
    """Run all integration tests"""
    print("LLAMAPARSE INTEGRATION TEST SUITE")
    print("=" * 60)
    
    # Test the integration
    success = await test_llamaparse_integration()
    
    if success:
        # Test with sample file
        await test_with_sample_file()
        
        # Print usage instructions
        print_usage_instructions()
    else:
        print("\n❌ Integration tests failed. Please fix the issues above.")

if __name__ == "__main__":
    asyncio.run(main())
