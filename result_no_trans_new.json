[{"file_path": "test_files_benchmark\\invocie_2.jpg", "file_name": "invocie_2.jpg", "classification": {"category": "invoice", "confidence_score": 0.95, "field_match_score": 0.9, "reasoning": "The document contains critical fields such as supplier name, supplier phone number, supplier company registration, supplier address, invoice number, date, total amount, customer name, and due date. The document is also titled 'INVOICE' and includes a request for payment, which are typical characteristics of an invoice.", "matched_fields": ["supplier_name", "supplier_phone_number", "supplier_company_registration", "supplier_address", "invoice_number", "date", "total_amount", "customer_name", "due_date"], "missing_critical_fields": ["total_net", "total_tax"], "alternative_categories": [{"category": "receipt", "confidence": 0.03}, {"category": "bank_check", "confidence": 0.02}], "overall_confidence_level": "HIGH"}}, {"file_path": "test_files_benchmark\\invoice_1.jpg", "file_name": "invoice_1.jpg", "classification": {"category": "invoice", "confidence_score": 0.95, "field_match_score": 0.9, "reasoning": "The document contains critical fields such as supplier name, supplier address, supplier phone number, supplier company registration, invoice number, date, and total amount which are characteristic of an invoice. The document also explicitly mentions 'INVOICE'.", "matched_fields": ["supplier_name", "supplier_address", "supplier_phone_number", "supplier_company_registration", "invoice_number", "date", "total_amount"], "missing_critical_fields": ["total_net", "total_tax", "customer_name", "due_date"], "alternative_categories": [{"category": "receipt", "confidence": 0.05}, {"category": "bank_check", "confidence": 0.0}], "overall_confidence_level": "HIGH"}}, {"file_path": "test_files_benchmark\\invoice_3.jpg", "file_name": "invoice_3.jpg", "classification": {"category": "invoice", "confidence_score": 0.95, "field_match_score": 0.9, "reasoning": "The document contains critical fields such as supplier_name, supplier_phone_number, supplier_company_registration, supplier_address, invoice_number, date, total_amount, customer_name, and due_date, which are all indicative of an invoice. The document also mentions 'invoice' in the text.", "matched_fields": ["supplier_name", "supplier_phone_number", "supplier_company_registration", "supplier_address", "invoice_number", "date", "total_amount", "customer_name", "due_date"], "missing_critical_fields": ["total_net", "total_tax"], "alternative_categories": [{"category": "receipt", "confidence": 0.05}, {"category": "bank_check", "confidence": 0.0}], "overall_confidence_level": "HIGH"}}, {"file_path": "test_files_benchmark\\receipt_1.jpg", "file_name": "receipt_1.jpg", "classification": {"category": "receipt", "confidence_score": 0.95, "field_match_score": 0.89, "reasoning": "The document contains fields that are specific to a receipt such as supplier name, supplier address, supplier phone number, receipt number, date, total amount, total net, and total tax. The document also mentions 'RECEIPT' at the top, which is a strong indicator that it is a receipt.", "matched_fields": ["supplier_name", "supplier_address", "supplier_phone_number", "receipt_number", "date", "total_amount", "total_net", "total_tax"], "missing_critical_fields": ["supplier_company_registration"], "alternative_categories": [{"category": "invoice", "confidence": 0.05}, {"category": "bank_check", "confidence": 0.0}], "overall_confidence_level": "HIGH"}}, {"file_path": "test_files_benchmark\\receipt_10.jpg", "file_name": "receipt_10.jpg", "classification": {"category": "receipt", "confidence_score": 0.95, "field_match_score": 0.89, "reasoning": "The document contains critical fields that match the receipt schema, such as supplier name, supplier address, supplier phone number, supplier company registration, receipt number, date, total amount, total net, and total tax. The document does not contain customer name and due date, which are critical fields in the invoice schema, and does not contain any fields from the bank check schema.", "matched_fields": ["supplier_name", "supplier_address", "supplier_phone_number", "supplier_company_registration", "receipt_number", "date", "total_amount", "total_net", "total_tax"], "missing_critical_fields": [], "alternative_categories": [{"category": "invoice", "confidence": 0.05}, {"category": "bank_check", "confidence": 0.0}], "overall_confidence_level": "HIGH"}}, {"file_path": "test_files_benchmark\\receipt_11.jpg", "file_name": "receipt_11.jpg", "classification": {"category": "receipt", "confidence_score": 0.95, "field_match_score": 0.5, "reasoning": "The document contains details typically found in a receipt such as supplier name, supplier address, date, and total amount. The document also mentions 'Pickup Receipt' which strongly indicates it is a receipt.", "matched_fields": ["supplier_name", "supplier_address", "date", "total_amount"], "missing_critical_fields": ["supplier_phone_number", "supplier_company_registration", "receipt_number", "total_net", "total_tax"], "alternative_categories": [{"category": "invoice", "confidence": 0.05}, {"category": "bank_check", "confidence": 0.0}], "overall_confidence_level": "MEDIUM"}}, {"file_path": "test_files_benchmark\\receipt_12.jpg", "file_name": "receipt_12.jpg", "classification": {"category": "receipt", "confidence_score": 0.95, "field_match_score": 0.75, "reasoning": "The document contains a list of purchased items with their prices, a total amount, and a date. It also includes the supplier's name, address, and phone number. However, it lacks a receipt number and tax details, which are typical for a receipt.", "matched_fields": ["supplier_name", "supplier_address", "supplier_phone_number", "date", "total_amount"], "missing_critical_fields": ["receipt_number", "total_net", "total_tax"], "alternative_categories": [{"category": "invoice", "confidence": 0.05}, {"category": "bank_check", "confidence": 0.0}], "overall_confidence_level": "HIGH"}}, {"file_path": "test_files_benchmark\\receipt_13.pdf", "file_name": "receipt_13.pdf", "classification": {"category": "receipt", "confidence_score": 0.95, "field_match_score": 0.75, "reasoning": "The document contains details typically found in a receipt such as supplier name, supplier address, supplier phone number, date, and total amount. However, it lacks some critical fields like receipt number, total net, and total tax.", "matched_fields": ["supplier_name", "supplier_address", "supplier_phone_number", "date", "total_amount"], "missing_critical_fields": ["receipt_number", "total_net", "total_tax"], "alternative_categories": [{"category": "invoice", "confidence": 0.05}, {"category": "bank_check", "confidence": 0.0}], "overall_confidence_level": "HIGH"}}, {"file_path": "test_files_benchmark\\receipt_14.pdf", "file_name": "receipt_14.pdf", "classification": {"category": "receipt", "confidence_score": 0.95, "field_match_score": 0.78, "reasoning": "The document contains details typically found in a receipt such as supplier name, supplier address, supplier phone number, date, and total amount. It also mentions purchased items and payment method, which are common in receipts. However, it lacks some critical fields like receipt number, total net, and total tax.", "matched_fields": ["supplier_name", "supplier_address", "supplier_phone_number", "date", "total_amount"], "missing_critical_fields": ["receipt_number", "total_net", "total_tax"], "alternative_categories": [{"category": "invoice", "confidence": 0.05}, {"category": "bank_check", "confidence": 0.0}], "overall_confidence_level": "HIGH"}}, {"file_path": "test_files_benchmark\\receipt_15.pdf", "file_name": "receipt_15.pdf", "classification": {"category": "receipt", "confidence_score": 0.95, "field_match_score": 0.44, "reasoning": "The document contains a list of purchased items with their prices, a total amount, and a payment method, which are typical characteristics of a receipt. However, it lacks supplier information such as name, address, and phone number.", "matched_fields": ["receipt_number", "date", "total_amount", "total_tax"], "missing_critical_fields": ["supplier_name", "supplier_address", "supplier_phone_number", "supplier_company_registration", "total_net"], "alternative_categories": [{"category": "invoice", "confidence": 0.03}, {"category": "bank_check", "confidence": 0.02}], "overall_confidence_level": "MEDIUM"}}, {"file_path": "test_files_benchmark\\receipt_16.jpg", "file_name": "receipt_16.jpg", "classification": {"category": "receipt", "confidence_score": 0.95, "field_match_score": 0.89, "reasoning": "The document contains critical fields that match the receipt schema such as supplier name, supplier address, supplier phone number, supplier company registration, date, total amount, total net, and total tax. The document also includes a list of purchased items with their quantities and prices, which is typical for a receipt. However, the receipt number is missing.", "matched_fields": ["supplier_name", "supplier_address", "supplier_phone_number", "supplier_company_registration", "date", "total_amount", "total_net", "total_tax"], "missing_critical_fields": ["receipt_number"], "alternative_categories": [{"category": "invoice", "confidence": 0.05}, {"category": "bank_check", "confidence": 0.0}], "overall_confidence_level": "HIGH"}}, {"file_path": "test_files_benchmark\\receipt_17.jpg", "file_name": "receipt_17.jpg", "classification": {"category": "invoice", "confidence_score": 0.95, "field_match_score": 0.9, "reasoning": "The document contains most of the critical fields for an invoice, including supplier name, supplier address, supplier phone number, supplier company registration, invoice number, date, total amount, total net, and total tax. However, it lacks customer name and due date.", "matched_fields": ["supplier_name", "supplier_address", "supplier_phone_number", "supplier_company_registration", "invoice_number", "date", "total_amount", "total_net", "total_tax"], "missing_critical_fields": ["customer_name", "due_date"], "alternative_categories": [{"category": "receipt", "confidence": 0.05}, {"category": "bank_check", "confidence": 0.0}], "overall_confidence_level": "HIGH"}}, {"file_path": "test_files_benchmark\\receipt_2.jpg", "file_name": "receipt_2.jpg", "classification": {"category": "invoice", "confidence_score": 0.95, "field_match_score": 0.9, "reasoning": "The document is titled 'Tax Invoice' and contains most of the critical fields for an invoice, including supplier details, invoice number, date, and total amounts. However, it lacks customer details and due date.", "matched_fields": ["supplier_name", "supplier_phone_number", "supplier_company_registration", "supplier_address", "invoice_number", "date", "total_amount", "total_net", "total_tax"], "missing_critical_fields": ["customer_name", "due_date"], "alternative_categories": [{"category": "receipt", "confidence": 0.05}, {"category": "bank_check", "confidence": 0.0}], "overall_confidence_level": "HIGH"}}, {"file_path": "test_files_benchmark\\receipt_3.jpg", "file_name": "receipt_3.jpg", "classification": {"category": "receipt", "confidence_score": 0.95, "field_match_score": 0.89, "reasoning": "The document contains most of the critical fields for a receipt, including supplier name, supplier address, supplier phone number, receipt number, date, total amount, total net, and total tax. However, it lacks the supplier company registration. The document also includes a list of purchased items, which is typical for a receipt.", "matched_fields": ["supplier_name", "supplier_address", "supplier_phone_number", "receipt_number", "date", "total_amount", "total_net", "total_tax"], "missing_critical_fields": ["supplier_company_registration"], "alternative_categories": [{"category": "invoice", "confidence": 0.05}, {"category": "bank_check", "confidence": 0.0}], "overall_confidence_level": "HIGH"}}, {"file_path": "test_files_benchmark\\receipt_4.jpg", "file_name": "receipt_4.jpg", "classification": {"category": "receipt", "confidence_score": 0.95, "field_match_score": 0.89, "reasoning": "The document contains critical fields such as supplier name, supplier address, supplier phone number, date, total amount, and total tax which are typical for a receipt. It also mentions a cashless transaction which is common in receipts. However, it lacks a receipt number.", "matched_fields": ["supplier_name", "supplier_address", "supplier_phone_number", "date", "total_amount", "total_tax"], "missing_critical_fields": ["receipt_number", "total_net", "supplier_company_registration"], "alternative_categories": [{"category": "invoice", "confidence": 0.05}, {"category": "bank_check", "confidence": 0.0}], "overall_confidence_level": "HIGH"}}, {"file_path": "test_files_benchmark\\receipt_5.jpg", "file_name": "receipt_5.jpg", "classification": {"category": "receipt", "confidence_score": 0.95, "field_match_score": 0.89, "reasoning": "The document contains most of the critical fields for a receipt, including supplier name, supplier address, supplier phone number, total amount, total net, total tax, and date. However, it lacks the receipt number and supplier company registration.", "matched_fields": ["supplier_name", "supplier_address", "supplier_phone_number", "total_amount", "total_net", "total_tax", "date"], "missing_critical_fields": ["receipt_number", "supplier_company_registration"], "alternative_categories": [{"category": "invoice", "confidence": 0.05}, {"category": "bank_check", "confidence": 0.0}], "overall_confidence_level": "HIGH"}}, {"file_path": "test_files_benchmark\\receipt_6.jpg", "file_name": "receipt_6.jpg", "classification": {"category": "receipt", "confidence_score": 0.95, "field_match_score": 0.78, "reasoning": "The document contains details typically found in a receipt such as supplier name, phone number, VAT number, list of purchased items with their prices, total amount, and a mention of 'TAX INVOICE'. However, it lacks some critical fields like supplier address, receipt number, and date.", "matched_fields": ["supplier_name", "supplier_phone_number", "supplier_company_registration", "total_amount", "total_net", "total_tax"], "missing_critical_fields": ["supplier_address", "receipt_number", "date"], "alternative_categories": [{"category": "invoice", "confidence": 0.05}, {"category": "bank_check", "confidence": 0.0}], "overall_confidence_level": "HIGH"}}, {"file_path": "test_files_benchmark\\receipt_7.jpg", "file_name": "receipt_7.jpg", "classification": {"category": "receipt", "confidence_score": 0.95, "field_match_score": 0.89, "reasoning": "The document contains critical fields that match the receipt schema such as supplier_name, supplier_address, supplier_phone_number, supplier_company_registration, receipt_number, date, total_amount, total_net, and total_tax. The context of the document also indicates a transaction that has already occurred, which is characteristic of a receipt.", "matched_fields": ["supplier_name", "supplier_address", "supplier_phone_number", "supplier_company_registration", "receipt_number", "date", "total_amount", "total_net", "total_tax"], "missing_critical_fields": [], "alternative_categories": [{"category": "invoice", "confidence": 0.05}, {"category": "bank_check", "confidence": 0.0}], "overall_confidence_level": "HIGH"}}, {"file_path": "test_files_benchmark\\receipt_8.jpg", "file_name": "receipt_8.jpg", "classification": {"category": "receipt", "confidence_score": 0.95, "field_match_score": 0.78, "reasoning": "The document contains critical fields such as supplier name, supplier phone number, supplier company registration, date, total amount, total net, and total tax which are common in a receipt. The document also mentions 'TAX INVOICE' but lacks critical fields for an invoice such as customer name and due date.", "matched_fields": ["supplier_name", "supplier_phone_number", "supplier_company_registration", "date", "total_amount", "total_net", "total_tax"], "missing_critical_fields": ["supplier_address", "receipt_number"], "alternative_categories": [{"category": "invoice", "confidence": 0.05}], "overall_confidence_level": "HIGH"}}, {"file_path": "test_files_benchmark\\receipt_9.jpg", "file_name": "receipt_9.jpg", "classification": {"category": "receipt", "confidence_score": 0.95, "field_match_score": 0.89, "reasoning": "The document contains most of the critical fields for a receipt, including supplier name, supplier address, supplier phone number, receipt number, date, total amount, total net, and total tax. However, it does not contain the supplier company registration.", "matched_fields": ["supplier_name", "supplier_address", "supplier_phone_number", "receipt_number", "date", "total_amount", "total_net", "total_tax"], "missing_critical_fields": ["supplier_company_registration"], "alternative_categories": [{"category": "invoice", "confidence": 0.05}, {"category": "bank_check", "confidence": 0.0}], "overall_confidence_level": "HIGH"}}]