[{"name": "given_names", "type": "string", "title": "Given Names", "is_array": false, "description": "The given names (first names) of the passport holder.", "nested_fields": [], "classification_values": []}, {"name": "surnames", "type": "string", "title": "Surnames", "is_array": false, "description": "The surnames (last names) of the passport holder.", "nested_fields": [], "classification_values": []}, {"name": "date_of_birth", "type": "date", "title": "Date of Birth", "is_array": false, "description": "The date of birth of the passport holder.", "nested_fields": [], "classification_values": []}, {"name": "place_of_birth", "type": "string", "title": "Place of Birth", "is_array": false, "description": "The place of birth of the passport holder.", "nested_fields": [], "classification_values": []}, {"name": "passport_number", "type": "string", "title": "Passport Number", "is_array": false, "description": "The passport number.", "nested_fields": [], "classification_values": []}, {"name": "issuing_country", "type": "string", "title": "Issuing Country", "is_array": false, "description": "The country that issued the passport.", "nested_fields": [], "classification_values": []}, {"name": "nationality", "type": "string", "title": "Nationality", "is_array": false, "description": "The nationality of the passport holder.", "nested_fields": [], "classification_values": []}, {"name": "date_of_issue", "type": "date", "title": "Date of Issue", "is_array": false, "description": "The date the passport was issued.", "nested_fields": [], "classification_values": []}, {"name": "date_of_expiry", "type": "date", "title": "Date of Expiry", "is_array": false, "description": "The date the passport expires.", "nested_fields": [], "classification_values": []}, {"name": "sex", "type": "classification", "title": "Sex", "is_array": false, "description": "The sex of the passport holder.", "nested_fields": [], "classification_values": ["Male", "Female", "Other"]}, {"name": "mrz_line_1", "type": "string", "title": "MRZ Line 1", "is_array": false, "description": "The first line of the Machine Readable Zone (MRZ).", "nested_fields": [], "classification_values": []}, {"name": "mrz_line_2", "type": "string", "title": "MRZ Line 2", "is_array": false, "description": "The second line of the Machine Readable Zone (MRZ).", "nested_fields": [], "classification_values": []}]