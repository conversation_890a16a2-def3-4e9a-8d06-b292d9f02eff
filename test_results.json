[{"file_path": "test_files\\sample_check.txt", "file_name": "sample_check.txt", "classification": {"category": "bank_check", "confidence_score": 0.88, "field_match_score": 0.9, "reasoning": "Document contains check-specific fields like 'pay to' and amount in dollars", "matched_fields": ["payer_name", "pay_to", "check_date", "number_amount"], "missing_critical_fields": ["word_amount"], "alternative_categories": [{"category": "receipt", "confidence": 0.08}, {"category": "invoice", "confidence": 0.04}], "overall_confidence_level": "HIGH"}}, {"file_path": "test_files\\sample_invoice.txt", "file_name": "sample_invoice.txt", "classification": {"category": "invoice", "confidence_score": 0.85, "field_match_score": 0.8, "reasoning": "Document contains invoice-specific terms like 'invoice number' and 'due date'", "matched_fields": ["invoice_number", "date", "total_amount"], "missing_critical_fields": ["supplier_name"], "alternative_categories": [{"category": "receipt", "confidence": 0.1}, {"category": "bank_check", "confidence": 0.05}], "overall_confidence_level": "HIGH"}}, {"file_path": "test_files\\sample_non_expense.txt", "file_name": "sample_non_expense.txt", "classification": {"category": "not_expense", "confidence_score": 0.75, "field_match_score": 0.1, "reasoning": "Document does not contain typical expense document fields or keywords", "matched_fields": [], "missing_critical_fields": ["all_critical_fields"], "alternative_categories": [{"category": "receipt", "confidence": 0.15}, {"category": "invoice", "confidence": 0.1}], "overall_confidence_level": "LOW"}}, {"file_path": "test_files\\sample_receipt.txt", "file_name": "sample_receipt.txt", "classification": {"category": "receipt", "confidence_score": 0.9, "field_match_score": 0.85, "reasoning": "Document shows proof of payment with total amount and appears to be a receipt", "matched_fields": ["supplier_name", "date", "total_amount", "time"], "missing_critical_fields": ["supplier_address"], "alternative_categories": [{"category": "invoice", "confidence": 0.08}, {"category": "bank_check", "confidence": 0.02}], "overall_confidence_level": "HIGH"}}]