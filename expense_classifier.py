"""
Expense File Classifier

A robust classifier that uses OCR (LlamaParse) to extract markdown from files,
then uses JSON schemas and LLM reasoning to classify expense documents with
multi-score confidence system.

Supported categories:
- Receipt
- Invoice  
- Bank Check
- Not an expense document
"""

import json
import os
import logging
from typing import Dict, List, Optional, Any, Tuple
from dataclasses import dataclass
from pathlib import Path
import asyncio

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

@dataclass
class ClassificationResult:
    """Result of document classification with confidence metrics"""
    category: str
    confidence_score: float
    field_match_score: float
    reasoning: str
    matched_fields: List[str]
    missing_critical_fields: List[str]
    alternative_categories: List[Dict[str, float]]
    overall_confidence_level: str  # HIGH/MEDIUM/LOW
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary for JSON serialization"""
        return {
            "category": self.category,
            "confidence_score": self.confidence_score,
            "field_match_score": self.field_match_score,
            "reasoning": self.reasoning,
            "matched_fields": self.matched_fields,
            "missing_critical_fields": self.missing_critical_fields,
            "alternative_categories": self.alternative_categories,
            "overall_confidence_level": self.overall_confidence_level
        }

class SchemaLoader:
    """Loads and manages JSON schemas for different document types"""
    
    def __init__(self, schema_dir: str = "."):
        self.schema_dir = Path(schema_dir)
        self.schemas = {}
        self.load_schemas()
    
    def load_schemas(self):
        """Load all available schemas"""
        schema_files = {
            "receipt": "receipts.json",
            "invoice": "invoice.json", 
            "bank_check": "bank_check.json"
        }
        
        for category, filename in schema_files.items():
            schema_path = self.schema_dir / filename
            if schema_path.exists():
                try:
                    with open(schema_path, 'r', encoding='utf-8') as f:
                        schema_data = json.load(f)
                    self.schemas[category] = self._process_schema(schema_data)
                    logger.info(f"Loaded schema for {category}")
                except Exception as e:
                    logger.error(f"Error loading schema {filename}: {e}")
            else:
                logger.warning(f"Schema file not found: {schema_path}")
    
    def _process_schema(self, schema_data: List[Dict]) -> Dict[str, Any]:
        """Process schema data into a more usable format"""
        processed = {
            "fields": {},
            "critical_fields": [],
            "optional_fields": []
        }
        
        for field in schema_data:
            field_name = field["name"]
            processed["fields"][field_name] = {
                "type": field["type"],
                "title": field["title"],
                "description": field["description"],
                "is_array": field.get("is_array", False)
            }
            
            # Determine critical vs optional fields based on common patterns
            if any(keyword in field_name.lower() for keyword in 
                   ["amount", "total", "date", "number", "name", "supplier", "payer"]):
                processed["critical_fields"].append(field_name)
            else:
                processed["optional_fields"].append(field_name)
        
        return processed
    
    def get_schema(self, category: str) -> Optional[Dict[str, Any]]:
        """Get schema for a specific category"""
        return self.schemas.get(category)
    
    def get_all_categories(self) -> List[str]:
        """Get list of all available categories"""
        return list(self.schemas.keys())

class ExpenseClassifier:
    """Main classifier that processes documents and returns classification results"""
    
    def __init__(self, schema_loader: SchemaLoader, llm_client=None):
        self.schema_loader = schema_loader
        self.llm_client = llm_client  # Placeholder for LLM client
        
    def _create_classification_prompt(self, markdown_text: str, schemas: Dict[str, Any]) -> str:
        """Create a comprehensive prompt for LLM classification"""
        
        prompt = f"""You are an expert document classifier specializing in expense documents. 

TASK: Analyze the following OCR-extracted markdown text and classify it into one of these categories:
- receipt: A document showing proof of purchase/payment for goods or services
- invoice: A document requesting payment for goods or services  
- bank_check: A bank check or cheque document
- not_expense: Not an expense-related document

DOCUMENT TEXT:
{markdown_text}

SCHEMAS TO MATCH AGAINST:
"""
        
        for category, schema in schemas.items():
            prompt += f"\n{category.upper()} SCHEMA - Critical Fields:\n"
            for field in schema["critical_fields"]:
                field_info = schema["fields"][field]
                prompt += f"- {field}: {field_info['description']}\n"
        
        prompt += """
INSTRUCTIONS:
1. Carefully analyze the document text for the presence of fields from each schema
2. Determine which category best matches based on field presence and document context
3. Provide confidence scores and detailed reasoning

RESPONSE FORMAT (JSON):
{
    "category": "receipt|invoice|bank_check|not_expense",
    "confidence_score": 0.85,
    "field_match_score": 0.90,
    "reasoning": "Brief explanation of why this category was chosen",
    "matched_fields": ["field1", "field2", "field3"],
    "missing_critical_fields": ["field4"],
    "alternative_categories": [
        {"category": "invoice", "confidence": 0.15},
        {"category": "bank_check", "confidence": 0.05}
    ]
}

SCORING GUIDELINES:
- confidence_score: Overall confidence in the classification (0-1)
- field_match_score: Percentage of critical fields found (0-1)
- Include top 2-3 alternative categories with their confidence scores
- Be specific about which fields were found and which critical ones are missing

Analyze the document now:"""
        
        return prompt

    def _determine_confidence_level(self, confidence_score: float, field_match_score: float) -> str:
        """Determine overall confidence level based on scores"""
        avg_score = (confidence_score + field_match_score) / 2

        if avg_score >= 0.8:
            return "HIGH"
        elif avg_score >= 0.5:
            return "MEDIUM"
        else:
            return "LOW"

    def _parse_llm_response(self, response_text: str) -> Dict[str, Any]:
        """Parse LLM response and extract classification data"""
        try:
            # Try to extract JSON from response
            import re
            json_match = re.search(r'\{.*\}', response_text, re.DOTALL)
            if json_match:
                json_str = json_match.group()
                return json.loads(json_str)
            else:
                raise ValueError("No JSON found in response")
        except Exception as e:
            logger.error(f"Error parsing LLM response: {e}")
            # Return default response for parsing errors
            return {
                "category": "not_expense",
                "confidence_score": 0.1,
                "field_match_score": 0.0,
                "reasoning": f"Error parsing LLM response: {e}",
                "matched_fields": [],
                "missing_critical_fields": [],
                "alternative_categories": []
            }

    async def classify_document(self, markdown_text: str) -> ClassificationResult:
        """
        Classify a document from its markdown text

        Args:
            markdown_text: OCR-extracted markdown text from the document

        Returns:
            ClassificationResult with confidence metrics
        """
        if not markdown_text or len(markdown_text.strip()) < 10:
            return ClassificationResult(
                category="not_expense",
                confidence_score=0.0,
                field_match_score=0.0,
                reasoning="Document text is too short or empty",
                matched_fields=[],
                missing_critical_fields=[],
                alternative_categories=[],
                overall_confidence_level="LOW"
            )

        # Get all schemas
        schemas = {}
        for category in self.schema_loader.get_all_categories():
            schema = self.schema_loader.get_schema(category)
            if schema:
                schemas[category] = schema

        if not schemas:
            logger.error("No schemas loaded")
            return ClassificationResult(
                category="not_expense",
                confidence_score=0.0,
                field_match_score=0.0,
                reasoning="No classification schemas available",
                matched_fields=[],
                missing_critical_fields=[],
                alternative_categories=[],
                overall_confidence_level="LOW"
            )

        # Create prompt and get LLM response
        prompt = self._create_classification_prompt(markdown_text, schemas)

        # Call LLM for classification
        if self.llm_client is None:
            raise ValueError("No LLM client provided. Please provide an LLM client for classification.")

        try:
            # Call the LLM client
            llm_response = await self.llm_client.classify(prompt)
        except Exception as e:
            logger.error(f"LLM classification failed: {e}")
            raise Exception(f"LLM classification failed: {e}")

        # Parse response
        parsed_response = self._parse_llm_response(llm_response)

        # Determine overall confidence level
        confidence_level = self._determine_confidence_level(
            parsed_response.get("confidence_score", 0.0),
            parsed_response.get("field_match_score", 0.0)
        )

        return ClassificationResult(
            category=parsed_response.get("category", "not_expense"),
            confidence_score=parsed_response.get("confidence_score", 0.0),
            field_match_score=parsed_response.get("field_match_score", 0.0),
            reasoning=parsed_response.get("reasoning", "No reasoning provided"),
            matched_fields=parsed_response.get("matched_fields", []),
            missing_critical_fields=parsed_response.get("missing_critical_fields", []),
            alternative_categories=parsed_response.get("alternative_categories", []),
            overall_confidence_level=confidence_level
        )



class LlamaParseOCR:
    """
    LlamaParse OCR integration using the existing llamaparse_extractor
    """

    def __init__(self, api_key: str):
        from llamaparse_extractor import LlamaIndexAPI
        self.api = LlamaIndexAPI(api_key)
        self.api_key = api_key
        logger.info("LlamaParseOCR initialized with existing llamaparse_extractor")

    async def extract_markdown(self, file_path: str) -> str:
        """
        Extract markdown from file using your existing LlamaParse implementation

        Args:
            file_path: Path to the file to process

        Returns:
            Extracted markdown text
        """
        import pathlib
        import time

        file_path_obj = pathlib.Path(file_path)

        # Check if file extension is supported
        from llamaparse_extractor import SUPPORTED_EXTENSIONS
        if file_path_obj.suffix.lower() not in SUPPORTED_EXTENSIONS:
            raise ValueError(f"Unsupported file extension: {file_path_obj.suffix}. Supported: {SUPPORTED_EXTENSIONS}")

        logger.info(f"Extracting markdown from {file_path} using LlamaParse")

        # Upload file and get job ID
        upload_response = self.api.upload_file(file_path_obj)
        if 'error' in upload_response:
            raise Exception(f"LlamaParse upload failed: {upload_response['error']}")

        job_id = upload_response.get('id') or upload_response.get('job_id')
        if not job_id:
            raise Exception(f"No job_id returned from LlamaParse. Response: {upload_response}")

        logger.info(f"LlamaParse job ID: {job_id}. Waiting for completion...")

        # Poll for completion (with timeout)
        for _ in range(60):  # Wait up to 5 minutes
            status_response = self.api.get_job_status(job_id)
            status = status_response.get('status')

            if status and status.lower() in ('completed', 'success'):
                logger.info(f"LlamaParse job completed for {file_path}")
                break
            elif status and status.lower() == 'failed':
                raise Exception(f"LlamaParse job failed: {status_response}")

            # Wait before next poll
            time.sleep(5)
        else:
            raise Exception(f"LlamaParse job timeout after 5 minutes for {file_path}")

        # Get the markdown result
        result_response = self.api.get_job_result_markdown(job_id)
        if 'error' in result_response:
            raise Exception(f"LlamaParse result fetch failed: {result_response['error']}")

        markdown = result_response.get('markdown') or result_response.get('result')
        if not markdown:
            raise Exception(f"No markdown content returned from LlamaParse. Response: {result_response}")

        logger.info(f"Successfully extracted {len(markdown)} characters of markdown from {file_path}")
        return markdown

class ExpenseFileClassifier:
    """
    Main application class that orchestrates OCR extraction and classification
    """

    def __init__(self, schema_dir: str = ".", llm_client=None, llamaparse_api_key: str = None):
        if not llamaparse_api_key:
            raise ValueError("llamaparse_api_key is required for OCR functionality")

        self.schema_loader = SchemaLoader(schema_dir)
        self.ocr = LlamaParseOCR(llamaparse_api_key)
        self.classifier = ExpenseClassifier(self.schema_loader, llm_client)

        logger.info("ExpenseFileClassifier initialized with real LlamaParse integration")
        logger.info(f"Loaded schemas for: {self.schema_loader.get_all_categories()}")

    async def classify_file(self, file_path: str) -> ClassificationResult:
        """
        Process a file end-to-end: OCR extraction + classification

        Args:
            file_path: Path to the file to classify

        Returns:
            ClassificationResult with confidence metrics
        """
        if not os.path.exists(file_path):
            raise FileNotFoundError(f"File not found: {file_path}")

        logger.info(f"Processing file: {file_path}")

        # Step 1: Extract markdown using OCR
        try:
            markdown_text = await self.ocr.extract_markdown(file_path)
            logger.info(f"OCR extraction completed, text length: {len(markdown_text)}")
        except Exception as e:
            logger.error(f"OCR extraction failed: {e}")
            return ClassificationResult(
                category="not_expense",
                confidence_score=0.0,
                field_match_score=0.0,
                reasoning=f"OCR extraction failed: {e}",
                matched_fields=[],
                missing_critical_fields=[],
                alternative_categories=[],
                overall_confidence_level="LOW"
            )

        # Step 2: Classify the document
        try:
            result = await self.classifier.classify_document(markdown_text)
            logger.info(f"Classification completed: {result.category} (confidence: {result.confidence_score:.2f})")
            return result
        except Exception as e:
            logger.error(f"Classification failed: {e}")
            return ClassificationResult(
                category="not_expense",
                confidence_score=0.0,
                field_match_score=0.0,
                reasoning=f"Classification failed: {e}",
                matched_fields=[],
                missing_critical_fields=[],
                alternative_categories=[],
                overall_confidence_level="LOW"
            )

    async def classify_batch(self, folder_path: str, output_file: Optional[str] = None) -> List[Dict[str, Any]]:
        """
        Process all files in a folder and classify them

        Args:
            folder_path: Path to folder containing files to classify
            output_file: Optional path to save results as JSON

        Returns:
            List of classification results
        """
        if not os.path.exists(folder_path):
            raise FileNotFoundError(f"Folder not found: {folder_path}")

        results = []
        folder = Path(folder_path)

        # Get all files in the folder
        files = [f for f in folder.iterdir() if f.is_file()]
        logger.info(f"Found {len(files)} files to process in {folder_path}")

        for file_path in files:
            try:
                result = await self.classify_file(str(file_path))
                file_result = {
                    "file_path": str(file_path),
                    "file_name": file_path.name,
                    "classification": result.to_dict()
                }
                results.append(file_result)

                logger.info(f"✓ {file_path.name}: {result.category} ({result.overall_confidence_level})")

            except Exception as e:
                logger.error(f"✗ {file_path.name}: Error - {e}")
                error_result = {
                    "file_path": str(file_path),
                    "file_name": file_path.name,
                    "classification": {
                        "category": "error",
                        "confidence_score": 0.0,
                        "field_match_score": 0.0,
                        "reasoning": f"Processing error: {e}",
                        "matched_fields": [],
                        "missing_critical_fields": [],
                        "alternative_categories": [],
                        "overall_confidence_level": "LOW"
                    }
                }
                results.append(error_result)

        # Save results if output file specified
        if output_file:
            try:
                with open(output_file, 'w', encoding='utf-8') as f:
                    json.dump(results, f, indent=2, ensure_ascii=False)
                logger.info(f"Results saved to {output_file}")
            except Exception as e:
                logger.error(f"Error saving results: {e}")

        return results

    def print_summary(self, results: List[Dict[str, Any]]):
        """Print a summary of classification results"""
        if not results:
            print("No results to summarize")
            return

        # Count by category
        category_counts = {}
        confidence_levels = {"HIGH": 0, "MEDIUM": 0, "LOW": 0}

        for result in results:
            classification = result["classification"]
            category = classification["category"]
            confidence_level = classification["overall_confidence_level"]

            category_counts[category] = category_counts.get(category, 0) + 1
            confidence_levels[confidence_level] = confidence_levels.get(confidence_level, 0) + 1

        print("\n" + "="*50)
        print("CLASSIFICATION SUMMARY")
        print("="*50)
        print(f"Total files processed: {len(results)}")
        print("\nBy Category:")
        for category, count in sorted(category_counts.items()):
            print(f"  {category}: {count}")

        print("\nBy Confidence Level:")
        for level, count in confidence_levels.items():
            print(f"  {level}: {count}")

        print("\nDetailed Results:")
        for result in results:
            classification = result["classification"]
            print(f"  {result['file_name']}: {classification['category']} "
                  f"({classification['overall_confidence_level']}) - "
                  f"{classification['reasoning'][:60]}...")


# Example usage and testing
async def main():
    """Example usage of the expense classifier"""

    # Get API key from environment
    import os
    llamaparse_api_key = os.getenv("LLAMAPARSE_API_KEY")

    if not llamaparse_api_key:
        print("❌ Error: LLAMAPARSE_API_KEY not found in environment variables.")
        print("Please set your LlamaParse API key:")
        print("export LLAMAPARSE_API_KEY='your_api_key_here'")
        return

    # Initialize classifier with real LlamaParse
    try:
        classifier = ExpenseFileClassifier(
            schema_dir=".",  # Directory containing JSON schemas
            llm_client=None,  # You need to provide an LLM client
            llamaparse_api_key=llamaparse_api_key
        )
    except Exception as e:
        print(f"❌ Error initializing classifier: {e}")
        print("Make sure you have:")
        print("1. Valid LLAMAPARSE_API_KEY")
        print("2. LLM client configured")
        return

    # Test folder path
    test_folder = "test_files"

    # Create test folder if it doesn't exist
    os.makedirs(test_folder, exist_ok=True)

    print(f"✅ Expense File Classifier Ready!")
    print(f"📁 Place your test files in: {os.path.abspath(test_folder)}")
    print(f"📋 Loaded schemas: {classifier.schema_loader.get_all_categories()}")
    print(f"✅ LlamaParse integration: ENABLED")

    from llamaparse_extractor import SUPPORTED_EXTENSIONS
    print(f"📄 Supported file types: {', '.join(SUPPORTED_EXTENSIONS)}")

    print(f"\n⚠️  Note: You need to provide an LLM client for classification.")
    print(f"⚠️  The classifier will fail without an LLM client.")

    # Check if test folder has files
    test_files = list(Path(test_folder).glob("*"))
    if test_files:
        print(f"\n🔍 Found {len(test_files)} files in test folder.")
        print(f"❌ Cannot process files without LLM client.")
        print(f"💡 Please use the integration_guide.py to set up an LLM client.")
        print(f"💡 Example: OpenAI, Anthropic, or local Ollama")
    else:
        print(f"\n📂 No files found in {test_folder}. Add some files and run again.")
        print(f"📄 Supported file types: PDF, DOC, DOCX, PNG, JPG, JPEG, TIFF, BMP, GIF")

if __name__ == "__main__":
    asyncio.run(main())
