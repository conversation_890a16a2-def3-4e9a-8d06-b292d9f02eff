"""
Integration Guide for Expense File Classifier

This file shows how to integrate real LlamaParse OCR and LLM clients
to replace the mock implementations in the classifier.
"""

import os
import asyncio
from typing import Optional
import json

# Example 1: LlamaParse Integration
class RealLlamaParseOCR:
    """Real LlamaParse OCR implementation"""
    
    def __init__(self, api_key: str):
        try:
            from llama_parse import LlamaParse
            self.parser = LlamaParse(
                api_key=api_key,
                result_type="markdown",  # Get markdown output
                verbose=True
            )
        except ImportError:
            raise ImportError("llama-parse not installed. Run: pip install llama-parse")
    
    async def extract_markdown(self, file_path: str) -> str:
        """Extract markdown from file using LlamaParse"""
        try:
            # Load and parse the document
            documents = await self.parser.aload_data(file_path)
            
            # Combine all document text
            markdown_text = ""
            for doc in documents:
                markdown_text += doc.text + "\n\n"
            
            return markdown_text.strip()
            
        except Exception as e:
            raise Exception(f"LlamaParse extraction failed: {e}")

# Example 2: OpenAI Integration
class OpenAIClassifier:
    """OpenAI GPT-based classifier"""
    
    def __init__(self, api_key: str, model: str = "gpt-4"):
        try:
            import openai
            self.client = openai.AsyncOpenAI(api_key=api_key)
            self.model = model
        except ImportError:
            raise ImportError("openai not installed. Run: pip install openai")
    
    async def classify(self, prompt: str) -> str:
        """Get classification from OpenAI"""
        try:
            response = await self.client.chat.completions.create(
                model=self.model,
                messages=[
                    {"role": "system", "content": "You are an expert document classifier. Always respond with valid JSON."},
                    {"role": "user", "content": prompt}
                ],
                temperature=0.1,  # Low temperature for consistent results
                max_tokens=1000
            )
            
            return response.choices[0].message.content
            
        except Exception as e:
            raise Exception(f"OpenAI classification failed: {e}")

# Example 3: Anthropic Claude Integration
class AnthropicClassifier:
    """Anthropic Claude-based classifier"""
    
    def __init__(self, api_key: str, model: str = "claude-3-sonnet-20240229"):
        try:
            import anthropic
            self.client = anthropic.AsyncAnthropic(api_key=api_key)
            self.model = model
        except ImportError:
            raise ImportError("anthropic not installed. Run: pip install anthropic")
    
    async def classify(self, prompt: str) -> str:
        """Get classification from Anthropic Claude"""
        try:
            response = await self.client.messages.create(
                model=self.model,
                max_tokens=1000,
                temperature=0.1,
                messages=[
                    {"role": "user", "content": prompt}
                ]
            )
            
            return response.content[0].text
            
        except Exception as e:
            raise Exception(f"Anthropic classification failed: {e}")

# Example 4: Local Ollama Integration
class OllamaClassifier:
    """Local Ollama-based classifier"""
    
    def __init__(self, model: str = "llama2", base_url: str = "http://localhost:11434"):
        try:
            import ollama
            self.client = ollama.AsyncClient(host=base_url)
            self.model = model
        except ImportError:
            raise ImportError("ollama not installed. Run: pip install ollama")
    
    async def classify(self, prompt: str) -> str:
        """Get classification from local Ollama"""
        try:
            response = await self.client.chat(
                model=self.model,
                messages=[
                    {"role": "system", "content": "You are an expert document classifier. Always respond with valid JSON."},
                    {"role": "user", "content": prompt}
                ],
                options={
                    "temperature": 0.1,
                    "num_predict": 1000
                }
            )
            
            return response['message']['content']
            
        except Exception as e:
            raise Exception(f"Ollama classification failed: {e}")

# Example 5: Integrated Expense Classifier with Real Services
class ProductionExpenseClassifier:
    """Production-ready expense classifier with real integrations"""
    
    def __init__(self, 
                 llamaparse_api_key: str,
                 llm_provider: str = "openai",
                 llm_api_key: Optional[str] = None,
                 llm_model: Optional[str] = None):
        
        # Initialize OCR
        self.ocr = RealLlamaParseOCR(llamaparse_api_key)
        
        # Initialize LLM client based on provider
        if llm_provider == "openai":
            model = llm_model or "gpt-4"
            self.llm = OpenAIClassifier(llm_api_key, model)
        elif llm_provider == "anthropic":
            model = llm_model or "claude-3-sonnet-20240229"
            self.llm = AnthropicClassifier(llm_api_key, model)
        elif llm_provider == "ollama":
            model = llm_model or "llama2"
            self.llm = OllamaClassifier(model)
        else:
            raise ValueError(f"Unsupported LLM provider: {llm_provider}")
        
        # Initialize the base classifier components
        from expense_classifier import SchemaLoader, ExpenseClassifier
        self.schema_loader = SchemaLoader(".")
        self.classifier = ExpenseClassifier(self.schema_loader, self.llm)
    
    async def classify_file(self, file_path: str):
        """Classify a file using real OCR and LLM"""
        # Extract markdown using real LlamaParse
        markdown_text = await self.ocr.extract_markdown(file_path)
        
        # Classify using real LLM
        result = await self.classifier.classify_document(markdown_text)
        
        return result

# Example 6: Environment-based Configuration
def create_classifier_from_env():
    """Create classifier using environment variables"""
    
    # Get API keys from environment
    llamaparse_key = os.getenv("LLAMAPARSE_API_KEY")
    openai_key = os.getenv("OPENAI_API_KEY")
    anthropic_key = os.getenv("ANTHROPIC_API_KEY")
    
    # Determine which LLM to use based on available keys
    if openai_key:
        return ProductionExpenseClassifier(
            llamaparse_api_key=llamaparse_key,
            llm_provider="openai",
            llm_api_key=openai_key,
            llm_model="gpt-4"
        )
    elif anthropic_key:
        return ProductionExpenseClassifier(
            llamaparse_api_key=llamaparse_key,
            llm_provider="anthropic", 
            llm_api_key=anthropic_key,
            llm_model="claude-3-sonnet-20240229"
        )
    else:
        # Fall back to local Ollama
        return ProductionExpenseClassifier(
            llamaparse_api_key=llamaparse_key,
            llm_provider="ollama",
            llm_model="llama2"
        )

# Example 7: Usage Examples
async def example_usage():
    """Example of how to use the production classifier"""
    
    # Method 1: Direct initialization
    classifier = ProductionExpenseClassifier(
        llamaparse_api_key="your_llamaparse_key",
        llm_provider="openai",
        llm_api_key="your_openai_key"
    )
    
    # Method 2: Environment-based initialization
    # classifier = create_classifier_from_env()
    
    # Classify a single file
    result = await classifier.classify_file("test_files/sample_receipt.pdf")
    print(f"Classification: {result.category} (confidence: {result.confidence_score:.2f})")
    
    # Batch processing would work the same way as the mock version

# Example 8: Error Handling and Retry Logic
class RobustExpenseClassifier(ProductionExpenseClassifier):
    """Enhanced classifier with error handling and retry logic"""
    
    async def classify_file_with_retry(self, file_path: str, max_retries: int = 3):
        """Classify file with retry logic for API failures"""
        
        for attempt in range(max_retries):
            try:
                return await self.classify_file(file_path)
            except Exception as e:
                if attempt == max_retries - 1:
                    # Last attempt failed, raise the error
                    raise e
                else:
                    # Wait before retrying
                    await asyncio.sleep(2 ** attempt)  # Exponential backoff
                    print(f"Attempt {attempt + 1} failed, retrying...")

if __name__ == "__main__":
    print("Integration Guide for Expense File Classifier")
    print("=" * 50)
    print("\nTo use real integrations:")
    print("1. Set environment variables:")
    print("   export LLAMAPARSE_API_KEY='your_key'")
    print("   export OPENAI_API_KEY='your_key'")
    print("   # or ANTHROPIC_API_KEY='your_key'")
    print("\n2. Replace the mock implementations in expense_classifier.py")
    print("3. Use the examples above as templates")
    print("\nSee README.md for detailed setup instructions.")
